2025-08-26 17:45:13,960 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-26 17:45:13,961 - INFO - [33mPress CTRL+C to quit[0m
2025-08-26 17:45:13,961 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:45:15,373 - WARNING -  * Debugger is active!
2025-08-26 17:45:15,379 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:45:18,636 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET / HTTP/1.1" 200 -
2025-08-26 17:45:18,744 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-26 17:45:18,751 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-26 17:45:19,300 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8Y_ HTTP/1.1" 200 -
2025-08-26 17:45:19,300 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-26 17:45:19,301 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:19,367 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "POST /socket.io/?EIO=4&transport=polling&t=PZbn8a1&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,371 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8a3&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,389 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8aO&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,433 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8b6&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:29,464 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\flask_socketio\\__init__.py', reloading
2025-08-26 17:45:29,465 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\engineio\\middleware.py', reloading
2025-08-26 17:45:29,472 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jinja2\\utils.py', reloading
2025-08-26 17:45:29,491 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:45:29,550 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-08-26 17:45:29,554 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-08-26 17:45:29,730 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /socket.io/?EIO=4&transport=websocket&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:29,734 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=u894XwNJEQrxJ4xdyooD HTTP/1.1" 200 -
2025-08-26 17:45:29,806 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-08-26 17:45:30,799 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:45:32,292 - WARNING -  * Debugger is active!
2025-08-26 17:45:32,297 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:45:32,306 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBL9 HTTP/1.1" 200 -
2025-08-26 17:45:32,321 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-26 17:45:32,322 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "POST /socket.io/?EIO=4&transport=polling&t=PZbnBkM&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,324 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBkN&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,450 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:32,457 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:32,515 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=websocket&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,589 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBod HTTP/1.1" 200 -
2025-08-26 17:45:32,592 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:32,600 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "POST /socket.io/?EIO=4&transport=polling&t=PZbnBoo&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:32,603 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBoo.0&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:32,631 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBpG&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:34,024 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-26 17:45:34,035 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=websocket&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:34,071 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,072 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,226 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCC6 HTTP/1.1" 200 -
2025-08-26 17:45:34,229 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:34,266 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCCr&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,269 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCCr.0&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,296 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCDJ&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,707 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-26 17:45:34,757 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,759 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,893 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCMb HTTP/1.1" 200 -
2025-08-26 17:45:34,917 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=websocket&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,930 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCN0&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:34,932 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCN0.0&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:34,975 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCNk&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:35,740 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /系统设置 HTTP/1.1" 200 -
2025-08-26 17:45:35,786 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:35,790 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:35,933 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCcs HTTP/1.1" 200 -
2025-08-26 17:45:35,982 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCdg&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:45:35,982 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=websocket&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:35,984 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCdg.0&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:45:36,022 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:36] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCeG&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:46:03,015 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:46:03,069 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-08-26 17:46:03,075 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-08-26 17:46:03,396 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=CFxdcrfSYBbGn5NvCh3R HTTP/1.1" 200 -
2025-08-26 17:46:03,401 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "GET /socket.io/?EIO=4&transport=websocket&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:47:31,795 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:31,796 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:31,796 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:32,871 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:47:34,080 - WARNING -  * Debugger is active!
2025-08-26 17:47:34,083 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:50:09,522 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py', reloading
2025-08-26 17:50:09,592 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py', reloading
2025-08-26 17:50:10,163 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:50:10,651 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:50:12,180 - WARNING -  * Debugger is active!
2025-08-26 17:50:12,183 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:50:16,727 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-26 17:50:16,727 - INFO - [33mPress CTRL+C to quit[0m
2025-08-26 17:50:16,727 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:50:17,894 - WARNING -  * Debugger is active!
2025-08-26 17:50:17,898 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:50:19,981 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:19] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:50:20,108 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:20] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-08-26 17:50:20,108 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:20] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-08-26 17:50:20,483 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:20] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=4jwdHsHQW8Fw4H7StngO HTTP/1.1" 200 -
2025-08-26 17:50:20,588 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:20] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-08-26 17:50:21,857 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:21] "GET /socket.io/?EIO=4&transport=polling&t=PZboIQ9 HTTP/1.1" 200 -
2025-08-26 17:50:21,891 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:21] "POST /socket.io/?EIO=4&transport=polling&t=PZboIQ-&sid=sduedRdGiPqIRbN4AAAA HTTP/1.1" 200 -
2025-08-26 17:50:21,892 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:21] "GET /socket.io/?EIO=4&transport=polling&t=PZboIQ_&sid=sduedRdGiPqIRbN4AAAA HTTP/1.1" 200 -
2025-08-26 17:50:21,917 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:21] "GET /socket.io/?EIO=4&transport=polling&t=PZboIRB&sid=sduedRdGiPqIRbN4AAAA HTTP/1.1" 200 -
2025-08-26 17:50:23,302 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:23] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:50:23,346 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:23] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-08-26 17:50:23,348 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:23] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-08-26 17:50:23,482 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:23] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=4jwdHsHQW8Fw4H7StngO HTTP/1.1[0m" 304 -
2025-08-26 17:50:23,483 - INFO - 127.0.0.1 - - [26/Aug/2025 17:50:23] "GET /socket.io/?EIO=4&transport=websocket&sid=sduedRdGiPqIRbN4AAAA HTTP/1.1" 200 -
2025-08-26 17:52:00,496 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:52:00,497 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:52:00,497 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:52:01,385 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:52:02,557 - WARNING -  * Debugger is active!
2025-08-26 17:52:02,560 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:52:47,349 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-26 17:52:47,349 - INFO - [33mPress CTRL+C to quit[0m
2025-08-26 17:52:47,349 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:52:48,595 - WARNING -  * Debugger is active!
2025-08-26 17:52:48,599 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:52:51,962 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:51] "GET /数据采集 HTTP/1.1" 200 -
2025-08-26 17:52:52,084 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:52:52,094 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:52:52,195 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot7U HTTP/1.1" 200 -
2025-08-26 17:52:52,195 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot7V HTTP/1.1" 200 -
2025-08-26 17:52:52,197 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:52:52,231 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "POST /socket.io/?EIO=4&transport=polling&t=PZbot82&sid=P4wpumCfLXzK80d6AAAA HTTP/1.1" 200 -
2025-08-26 17:52:52,232 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot83&sid=P4wpumCfLXzK80d6AAAA HTTP/1.1" 200 -
2025-08-26 17:52:52,239 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "POST /socket.io/?EIO=4&transport=polling&t=PZbot83.0&sid=bubGr3JQoNbjeF3YAAAB HTTP/1.1" 200 -
2025-08-26 17:52:52,243 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot83.1&sid=bubGr3JQoNbjeF3YAAAB HTTP/1.1" 200 -
2025-08-26 17:52:52,256 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot8T&sid=P4wpumCfLXzK80d6AAAA HTTP/1.1" 200 -
2025-08-26 17:52:52,268 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot8Y&sid=bubGr3JQoNbjeF3YAAAB HTTP/1.1" 200 -
2025-08-26 17:52:52,269 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:52] "GET /socket.io/?EIO=4&transport=polling&t=PZbot8c&sid=P4wpumCfLXzK80d6AAAA HTTP/1.1" 200 -
2025-08-26 17:52:53,135 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=polling&t=PZbotM9 HTTP/1.1" 200 -
2025-08-26 17:52:53,156 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "POST /socket.io/?EIO=4&transport=polling&t=PZbotMU&sid=uwGDNx0GsjCPfXyVAAAE HTTP/1.1" 200 -
2025-08-26 17:52:53,158 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=websocket&sid=bubGr3JQoNbjeF3YAAAB HTTP/1.1" 200 -
2025-08-26 17:52:53,159 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=websocket&sid=P4wpumCfLXzK80d6AAAA HTTP/1.1" 200 -
2025-08-26 17:52:53,162 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=polling&t=PZbotMV&sid=uwGDNx0GsjCPfXyVAAAE HTTP/1.1" 200 -
2025-08-26 17:52:53,206 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=polling&t=PZbotNB&sid=uwGDNx0GsjCPfXyVAAAE HTTP/1.1" 200 -
2025-08-26 17:52:53,724 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /系统设置 HTTP/1.1" 200 -
2025-08-26 17:52:53,729 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=websocket&sid=uwGDNx0GsjCPfXyVAAAE HTTP/1.1" 200 -
2025-08-26 17:52:53,763 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:52:53,765 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:52:53,927 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=polling&t=PZbotYb HTTP/1.1" 200 -
2025-08-26 17:52:53,958 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "POST /socket.io/?EIO=4&transport=polling&t=PZbotYt&sid=Yx5E60cSD0q0ODxPAAAG HTTP/1.1" 200 -
2025-08-26 17:52:53,959 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:53] "GET /socket.io/?EIO=4&transport=polling&t=PZbotYu&sid=Yx5E60cSD0q0ODxPAAAG HTTP/1.1" 200 -
2025-08-26 17:52:54,165 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET / HTTP/1.1" 200 -
2025-08-26 17:52:54,226 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:52:54,228 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:52:54,379 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /socket.io/?EIO=4&transport=polling&t=PZbotfc HTTP/1.1" 200 -
2025-08-26 17:52:54,381 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-26 17:52:54,382 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:52:54,419 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "POST /socket.io/?EIO=4&transport=polling&t=PZbotgE&sid=VjMl87plAljYx7gqAAAI HTTP/1.1" 200 -
2025-08-26 17:52:54,420 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /socket.io/?EIO=4&transport=websocket&sid=Yx5E60cSD0q0ODxPAAAG HTTP/1.1" 200 -
2025-08-26 17:52:54,420 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /socket.io/?EIO=4&transport=polling&t=PZbotgF&sid=VjMl87plAljYx7gqAAAI HTTP/1.1" 200 -
2025-08-26 17:52:54,448 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /socket.io/?EIO=4&transport=polling&t=PZbotgY&sid=VjMl87plAljYx7gqAAAI HTTP/1.1" 200 -
2025-08-26 17:52:54,854 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "GET /数据采集 HTTP/1.1" 200 -
2025-08-26 17:52:54,892 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:52:54,894 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:54] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:52:55,016 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=websocket&sid=VjMl87plAljYx7gqAAAI HTTP/1.1" 200 -
2025-08-26 17:52:55,020 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotpW HTTP/1.1" 200 -
2025-08-26 17:52:55,021 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotpX HTTP/1.1" 200 -
2025-08-26 17:52:55,023 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:52:55,059 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "POST /socket.io/?EIO=4&transport=polling&t=PZbotqC&sid=oihXbOS02LzAnDcGAAAK HTTP/1.1" 200 -
2025-08-26 17:52:55,061 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotqD&sid=oihXbOS02LzAnDcGAAAK HTTP/1.1" 200 -
2025-08-26 17:52:55,068 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "POST /socket.io/?EIO=4&transport=polling&t=PZbotqG&sid=EmDRzZaeJWAnwg42AAAL HTTP/1.1" 200 -
2025-08-26 17:52:55,069 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotqG.0&sid=EmDRzZaeJWAnwg42AAAL HTTP/1.1" 200 -
2025-08-26 17:52:55,083 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotqS&sid=oihXbOS02LzAnDcGAAAK HTTP/1.1" 200 -
2025-08-26 17:52:55,091 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotql&sid=EmDRzZaeJWAnwg42AAAL HTTP/1.1" 200 -
2025-08-26 17:52:55,105 - INFO - 127.0.0.1 - - [26/Aug/2025 17:52:55] "GET /socket.io/?EIO=4&transport=polling&t=PZbotqz&sid=EmDRzZaeJWAnwg42AAAL HTTP/1.1" 200 -
2025-08-26 17:53:00,550 - INFO - [INFO] 开始采集卖家 2219812363424 的商品数据...
2025-08-26 17:53:00,550 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:00] "POST /api/开始采集 HTTP/1.1" 200 -
2025-08-26 17:53:01,189 - INFO - [INFO] 已采集第 1 页，当前共有 20 个商品
2025-08-26 17:53:02,234 - INFO - [INFO] 已采集第 2 页，当前共有 40 个商品
2025-08-26 17:53:03,293 - INFO - [INFO] 已采集第 3 页，当前共有 60 个商品
2025-08-26 17:53:04,309 - INFO - [INFO] 已采集第 4 页，当前共有 80 个商品
2025-08-26 17:53:05,308 - INFO - [INFO] 已采集第 5 页，当前共有 100 个商品
2025-08-26 17:53:06,331 - INFO - [INFO] 已采集第 6 页，当前共有 120 个商品
2025-08-26 17:53:07,353 - INFO - [INFO] 已采集第 7 页，当前共有 140 个商品
2025-08-26 17:53:08,416 - INFO - [INFO] 已采集第 8 页，当前共有 153 个商品
2025-08-26 17:53:09,358 - INFO - [INFO] 已到达最后一页，共采集 8 页
2025-08-26 17:53:09,369 - INFO - [INFO] 商品数据已保存到: g:\代码处理工具\闲鱼商品采集 - 重构\WEB版本\数据\卖家数据\卖家_2219812363424.json
2025-08-26 17:53:09,369 - INFO - [SUCCESS] 采集完成，共采集到 153 个商品，其中新上架 0 个商品
2025-08-26 17:53:09,370 - INFO - [INFO] 采集任务结束
2025-08-26 17:53:15,408 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /数据查看 HTTP/1.1" 200 -
2025-08-26 17:53:15,448 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:53:15,451 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:53:15,574 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /socket.io/?EIO=4&transport=websocket&sid=oihXbOS02LzAnDcGAAAK HTTP/1.1" 200 -
2025-08-26 17:53:15,577 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /socket.io/?EIO=4&transport=websocket&sid=EmDRzZaeJWAnwg42AAAL HTTP/1.1" 200 -
2025-08-26 17:53:15,579 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /socket.io/?EIO=4&transport=polling&t=PZboyqj HTTP/1.1" 200 -
2025-08-26 17:53:15,618 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "POST /socket.io/?EIO=4&transport=polling&t=PZboyrR&sid=m4Zqe5x_KrbLoBfrAAAO HTTP/1.1" 200 -
2025-08-26 17:53:15,618 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /socket.io/?EIO=4&transport=polling&t=PZboyrR.0&sid=m4Zqe5x_KrbLoBfrAAAO HTTP/1.1" 200 -
2025-08-26 17:53:15,638 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:15] "GET /socket.io/?EIO=4&transport=polling&t=PZboyrq&sid=m4Zqe5x_KrbLoBfrAAAO HTTP/1.1" 200 -
2025-08-26 17:53:36,773 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:36] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-26 17:53:36,811 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:36] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:53:36,811 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:36] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:53:37,017 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp23t HTTP/1.1" 200 -
2025-08-26 17:53:37,019 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:53:37,041 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "POST /socket.io/?EIO=4&transport=polling&t=PZbp24C&sid=cpgD-ZL5utskvlfCAAAQ HTTP/1.1" 200 -
2025-08-26 17:53:37,043 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=websocket&sid=m4Zqe5x_KrbLoBfrAAAO HTTP/1.1" 200 -
2025-08-26 17:53:37,044 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp24D&sid=cpgD-ZL5utskvlfCAAAQ HTTP/1.1" 200 -
2025-08-26 17:53:37,065 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp24Z&sid=cpgD-ZL5utskvlfCAAAQ HTTP/1.1" 200 -
2025-08-26 17:53:37,511 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-26 17:53:37,554 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:53:37,555 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:53:37,686 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2EI HTTP/1.1" 200 -
2025-08-26 17:53:37,712 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=websocket&sid=cpgD-ZL5utskvlfCAAAQ HTTP/1.1" 200 -
2025-08-26 17:53:37,713 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "POST /socket.io/?EIO=4&transport=polling&t=PZbp2Ei&sid=pZ4-11_uVXt2_IH8AAAS HTTP/1.1" 200 -
2025-08-26 17:53:37,715 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2Ej&sid=pZ4-11_uVXt2_IH8AAAS HTTP/1.1" 200 -
2025-08-26 17:53:37,733 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:37] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2F1&sid=pZ4-11_uVXt2_IH8AAAS HTTP/1.1" 200 -
2025-08-26 17:53:38,540 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "GET /系统设置 HTTP/1.1" 200 -
2025-08-26 17:53:38,577 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:53:38,578 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:53:38,703 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2U7 HTTP/1.1" 200 -
2025-08-26 17:53:38,742 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "GET /socket.io/?EIO=4&transport=websocket&sid=pZ4-11_uVXt2_IH8AAAS HTTP/1.1" 200 -
2025-08-26 17:53:38,746 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "POST /socket.io/?EIO=4&transport=polling&t=PZbp2Ut&sid=-lUZ1Rj0pkj2ljMCAAAU HTTP/1.1" 200 -
2025-08-26 17:53:38,747 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2Ut.0&sid=-lUZ1Rj0pkj2ljMCAAAU HTTP/1.1" 200 -
2025-08-26 17:53:38,763 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:38] "GET /socket.io/?EIO=4&transport=polling&t=PZbp2V1&sid=-lUZ1Rj0pkj2ljMCAAAU HTTP/1.1" 200 -
2025-08-26 17:53:43,288 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "GET /数据查看 HTTP/1.1" 200 -
2025-08-26 17:53:43,325 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:53:43,327 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:53:43,449 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "GET /socket.io/?EIO=4&transport=polling&t=PZbp3eJ HTTP/1.1" 200 -
2025-08-26 17:53:43,451 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "GET /socket.io/?EIO=4&transport=websocket&sid=-lUZ1Rj0pkj2ljMCAAAU HTTP/1.1" 200 -
2025-08-26 17:53:43,487 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "POST /socket.io/?EIO=4&transport=polling&t=PZbp3ex&sid=ihBq-meiduxgSpjwAAAW HTTP/1.1" 200 -
2025-08-26 17:53:43,488 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "GET /socket.io/?EIO=4&transport=polling&t=PZbp3ex.0&sid=ihBq-meiduxgSpjwAAAW HTTP/1.1" 200 -
2025-08-26 17:53:43,505 - INFO - 127.0.0.1 - - [26/Aug/2025 17:53:43] "GET /socket.io/?EIO=4&transport=polling&t=PZbp3f7&sid=ihBq-meiduxgSpjwAAAW HTTP/1.1" 200 -
