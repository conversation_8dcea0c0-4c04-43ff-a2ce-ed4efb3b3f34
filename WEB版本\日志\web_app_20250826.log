2025-08-26 17:45:13,960 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-26 17:45:13,961 - INFO - [33mPress CTRL+C to quit[0m
2025-08-26 17:45:13,961 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:45:15,373 - WARNING -  * Debugger is active!
2025-08-26 17:45:15,379 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:45:18,636 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET / HTTP/1.1" 200 -
2025-08-26 17:45:18,744 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET /static/css/主样式.css HTTP/1.1" 200 -
2025-08-26 17:45:18,751 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:18] "GET /static/js/主脚本.js HTTP/1.1" 200 -
2025-08-26 17:45:19,300 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8Y_ HTTP/1.1" 200 -
2025-08-26 17:45:19,300 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /api/采集状态 HTTP/1.1" 200 -
2025-08-26 17:45:19,301 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:19,367 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "POST /socket.io/?EIO=4&transport=polling&t=PZbn8a1&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,371 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8a3&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,389 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8aO&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:19,433 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:19] "GET /socket.io/?EIO=4&transport=polling&t=PZbn8b6&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:29,464 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\flask_socketio\\__init__.py', reloading
2025-08-26 17:45:29,465 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\engineio\\middleware.py', reloading
2025-08-26 17:45:29,472 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jinja2\\utils.py', reloading
2025-08-26 17:45:29,491 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:45:29,550 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-08-26 17:45:29,554 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-08-26 17:45:29,730 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /socket.io/?EIO=4&transport=websocket&sid=uiQJ6b59LbsvAwSnAAAA HTTP/1.1" 200 -
2025-08-26 17:45:29,734 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=u894XwNJEQrxJ4xdyooD HTTP/1.1" 200 -
2025-08-26 17:45:29,806 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:29] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-08-26 17:45:30,799 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:45:32,292 - WARNING -  * Debugger is active!
2025-08-26 17:45:32,297 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:45:32,306 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBL9 HTTP/1.1" 200 -
2025-08-26 17:45:32,321 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-26 17:45:32,322 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "POST /socket.io/?EIO=4&transport=polling&t=PZbnBkM&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,324 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBkN&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,450 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:32,457 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:32,515 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=websocket&sid=k9EZwOMqagF16C2bAAAA HTTP/1.1" 200 -
2025-08-26 17:45:32,589 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBod HTTP/1.1" 200 -
2025-08-26 17:45:32,592 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:32,600 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "POST /socket.io/?EIO=4&transport=polling&t=PZbnBoo&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:32,603 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBoo.0&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:32,631 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:32] "GET /socket.io/?EIO=4&transport=polling&t=PZbnBpG&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:34,024 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /AI提纯 HTTP/1.1" 200 -
2025-08-26 17:45:34,035 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=websocket&sid=6-IEm_HHvNnqhLEzAAAC HTTP/1.1" 200 -
2025-08-26 17:45:34,071 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,072 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,226 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCC6 HTTP/1.1" 200 -
2025-08-26 17:45:34,229 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /api/卖家列表 HTTP/1.1" 200 -
2025-08-26 17:45:34,266 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCCr&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,269 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCCr.0&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,296 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCDJ&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,707 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /新上架商品 HTTP/1.1" 200 -
2025-08-26 17:45:34,757 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,759 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:34,893 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCMb HTTP/1.1" 200 -
2025-08-26 17:45:34,917 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=websocket&sid=A3xEFP8RM5JAFSjzAAAE HTTP/1.1" 200 -
2025-08-26 17:45:34,930 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCN0&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:34,932 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCN0.0&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:34,975 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:34] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCNk&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:35,740 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /系统设置 HTTP/1.1" 200 -
2025-08-26 17:45:35,786 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "[36mGET /static/css/主样式.css HTTP/1.1[0m" 304 -
2025-08-26 17:45:35,790 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "[36mGET /static/js/主脚本.js HTTP/1.1[0m" 304 -
2025-08-26 17:45:35,933 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCcs HTTP/1.1" 200 -
2025-08-26 17:45:35,982 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "POST /socket.io/?EIO=4&transport=polling&t=PZbnCdg&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:45:35,982 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=websocket&sid=Up5HYx_ijxP4Oaz7AAAG HTTP/1.1" 200 -
2025-08-26 17:45:35,984 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:35] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCdg.0&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:45:36,022 - INFO - 127.0.0.1 - - [26/Aug/2025 17:45:36] "GET /socket.io/?EIO=4&transport=polling&t=PZbnCeG&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:46:03,015 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[35m[1mGET /数据采集 HTTP/1.1[0m" 500 -
2025-08-26 17:46:03,069 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-08-26 17:46:03,075 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "[36mGET /数据采集?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-08-26 17:46:03,396 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "GET /数据采集?__debugger__=yes&cmd=resource&f=console.png&s=CFxdcrfSYBbGn5NvCh3R HTTP/1.1" 200 -
2025-08-26 17:46:03,401 - INFO - 127.0.0.1 - - [26/Aug/2025 17:46:03] "GET /socket.io/?EIO=4&transport=websocket&sid=-_XGA7UtoWe42_xRAAAI HTTP/1.1" 200 -
2025-08-26 17:47:31,795 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:31,796 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:31,796 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:47:32,871 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:47:34,080 - WARNING -  * Debugger is active!
2025-08-26 17:47:34,083 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:50:09,522 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py', reloading
2025-08-26 17:50:09,592 - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py', reloading
2025-08-26 17:50:10,163 - INFO -  * Detected change in 'g:\\代码处理工具\\闲鱼商品采集 - 重构\\WEB版本\\测试.py', reloading
2025-08-26 17:50:10,651 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:50:12,180 - WARNING -  * Debugger is active!
2025-08-26 17:50:12,183 - INFO -  * Debugger PIN: 423-099-573
2025-08-26 17:50:16,727 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-26 17:50:16,727 - INFO - [33mPress CTRL+C to quit[0m
2025-08-26 17:50:16,727 - INFO -  * Restarting with watchdog (windowsapi)
2025-08-26 17:50:17,894 - WARNING -  * Debugger is active!
2025-08-26 17:50:17,898 - INFO -  * Debugger PIN: 423-099-573
