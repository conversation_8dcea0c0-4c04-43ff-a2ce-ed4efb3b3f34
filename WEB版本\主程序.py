#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品采集工具 - WEB版本主程序
基于Flask框架的Web应用程序
"""

from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_socketio import SocketIO, emit
import os
import sys
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
from werkzeug.utils import secure_filename

# 添加modules目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from modules.数据采集 import 数据采集器
from modules.数据处理 import 数据处理器
from modules.配置管理 import 配置管理器
from modules.Cookie管理 import Cookie管理器
from modules.ai_提纯 import AI提纯器

class 闲鱼采集Web应用:
    def __init__(self):
        # 创建Flask应用
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'xianyu_collection_web_2024'
        
        # 创建SocketIO实例用于实时通信
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # 创建必要的文件夹结构
        self.创建文件夹结构()
        
        # 初始化组件
        self.配置管理器 = 配置管理器()
        self.配置管理器.设置配置目录(self.配置目录)
        
        self.cookie管理器 = Cookie管理器()
        self.cookie管理器.设置配置目录(self.配置目录)
        self.cookie管理器.设置配置管理器(self.配置管理器)
        
        self.数据采集器 = 数据采集器()
        self.数据采集器.设置Cookie管理器(self.cookie管理器)
        
        self.数据处理器 = 数据处理器()
        self.数据处理器.设置输出目录(self.卖家数据目录, self.新上架目录)
        self.数据处理器.设置配置管理器(self.配置管理器)
        
        self.ai提纯器 = AI提纯器()
        self.ai提纯器.设置提纯数据目录(self.提纯数据目录)
        self.ai提纯器.设置配置目录(self.配置目录)
        
        # 应用状态
        self.采集状态 = "空闲"
        self.当前卖家ID = ""
        self.采集结果 = []
        self.批量采集中 = False
        self.最大线程数 = 5
        
        # 多线程相关
        self.采集结果锁 = threading.Lock()
        
        # 设置日志
        self.设置日志()
        
        # 注册路由和事件处理器
        self.注册路由()
        self.注册Socket事件()
        
    def 创建文件夹结构(self):
        """创建项目所需的文件夹结构"""
        self.项目根目录 = os.path.dirname(os.path.abspath(__file__))
        self.数据目录 = os.path.join(self.项目根目录, "数据")
        self.卖家数据目录 = os.path.join(self.数据目录, "卖家数据")
        self.新上架目录 = os.path.join(self.数据目录, "新上架商品")
        self.提纯数据目录 = os.path.join(self.数据目录, "提纯结果")
        self.配置目录 = os.path.join(self.项目根目录, "配置")
        self.日志目录 = os.path.join(self.项目根目录, "日志")
        
        # 创建文件夹
        for 目录 in [self.数据目录, self.卖家数据目录, self.新上架目录, 
                    self.提纯数据目录, self.配置目录, self.日志目录]:
            if not os.path.exists(目录):
                os.makedirs(目录)
                
        # 移动现有配置文件
        self.移动现有文件()
        
    def 移动现有文件(self):
        """移动现有文件到对应目录"""
        # 从GUI版本复制配置文件
        gui_配置目录 = os.path.join(os.path.dirname(self.项目根目录), "GUI旧版本", "配置")
        gui_数据目录 = os.path.join(os.path.dirname(self.项目根目录), "GUI旧版本", "数据")
        
        # 复制配置文件
        配置文件列表 = ['闲鱼配置.ini', 'cookie_config.json', 'api_keys.txt']
        for 文件名 in 配置文件列表:
            源路径 = os.path.join(gui_配置目录, 文件名)
            目标路径 = os.path.join(self.配置目录, 文件名)
            
            if os.path.exists(源路径) and not os.path.exists(目标路径):
                try:
                    with open(源路径, 'r', encoding='utf-8') as 源:
                        with open(目标路径, 'w', encoding='utf-8') as 目标:
                            目标.write(源.read())
                    print(f"已复制配置文件 {文件名}")
                except Exception as e:
                    print(f"复制配置文件 {文件名} 时出错: {e}")
        
        # 复制数据文件
        if os.path.exists(gui_数据目录):
            for 子目录 in ['卖家数据', '新上架商品', '提纯结果']:
                源子目录 = os.path.join(gui_数据目录, 子目录)
                目标子目录 = os.path.join(self.数据目录, 子目录)
                
                if os.path.exists(源子目录):
                    for 文件名 in os.listdir(源子目录):
                        源文件路径 = os.path.join(源子目录, 文件名)
                        目标文件路径 = os.path.join(目标子目录, 文件名)
                        
                        if os.path.isfile(源文件路径) and not os.path.exists(目标文件路径):
                            try:
                                with open(源文件路径, 'r', encoding='utf-8') as 源:
                                    with open(目标文件路径, 'w', encoding='utf-8') as 目标:
                                        目标.write(源.read())
                                print(f"已复制数据文件 {文件名}")
                            except Exception as e:
                                print(f"复制数据文件 {文件名} 时出错: {e}")
    
    def 设置日志(self):
        """设置日志配置"""
        log_file = os.path.join(self.日志目录, f"web_app_{datetime.now().strftime('%Y%m%d')}.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def 发送实时消息(self, 消息: str, 消息类型: str = "info"):
        """通过WebSocket发送实时消息到前端"""
        try:
            self.socketio.emit('status_update', {
                'message': 消息,
                'type': 消息类型,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            self.logger.info(f"[{消息类型.upper()}] {消息}")
        except Exception as e:
            self.logger.error(f"发送实时消息失败: {e}")
    
    def 注册路由(self):
        """注册Flask路由"""
        
        @self.app.route('/')
        def 首页():
            return render_template('主页.html')
        
        @self.app.route('/数据采集')
        def 数据采集页面():
            卖家列表 = self.配置管理器.获取所有卖家()
            return render_template('数据采集.html', 卖家列表=卖家列表)
        
        @self.app.route('/数据查看')
        def 数据查看页面():
            return render_template('数据查看.html')
        
        @self.app.route('/AI提纯')
        def AI提纯页面():
            return render_template('AI提纯.html')
        
        @self.app.route('/系统设置')
        def 系统设置页面():
            return render_template('系统设置.html')
        
        @self.app.route('/新上架商品')
        def 新上架商品页面():
            return render_template('新上架商品.html')
        
        # API路由
        @self.app.route('/api/卖家列表')
        def api_获取卖家列表():
            try:
                卖家列表 = self.配置管理器.获取所有卖家()
                return jsonify({'success': True, 'data': 卖家列表})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/添加卖家', methods=['POST'])
        def api_添加卖家():
            try:
                data = request.get_json()
                卖家ID = data.get('卖家ID')
                卖家名称 = data.get('卖家名称')
                
                if not 卖家ID or not 卖家名称:
                    return jsonify({'success': False, 'error': '卖家ID和名称不能为空'})
                
                self.配置管理器.添加卖家(卖家ID, 卖家名称)
                return jsonify({'success': True, 'message': '卖家添加成功'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/删除卖家', methods=['POST'])
        def api_删除卖家():
            try:
                data = request.get_json()
                卖家ID = data.get('卖家ID')
                
                if not 卖家ID:
                    return jsonify({'success': False, 'error': '卖家ID不能为空'})
                
                self.配置管理器.删除卖家(卖家ID)
                return jsonify({'success': True, 'message': '卖家删除成功'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/开始采集', methods=['POST'])
        def api_开始采集():
            try:
                if self.采集状态 == "运行中":
                    return jsonify({'success': False, 'error': '采集正在进行中'})
                
                data = request.get_json()
                卖家ID = data.get('卖家ID')
                页数 = int(data.get('页数', 0))
                只采集有想要 = data.get('只采集有想要', False)
                只保存ID = data.get('只保存ID', False)
                
                if not 卖家ID:
                    return jsonify({'success': False, 'error': '请选择要采集的卖家'})
                
                # 在新线程中开始采集
                采集线程 = threading.Thread(
                    target=self._执行采集,
                    args=(卖家ID, 页数, 只采集有想要, 只保存ID)
                )
                采集线程.daemon = True
                采集线程.start()
                
                return jsonify({'success': True, 'message': '采集已开始'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})
        
        @self.app.route('/api/停止采集', methods=['POST'])
        def api_停止采集():
            try:
                self.采集状态 = "空闲"
                self.批量采集中 = False
                return jsonify({'success': True, 'message': '采集已停止'})
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/采集状态')
        def api_获取采集状态():
            return jsonify({
                'status': self.采集状态,
                'current_seller': self.当前卖家ID,
                'batch_collecting': self.批量采集中
            })

        @self.app.route('/api/数据统计')
        def api_获取数据统计():
            try:
                # 获取数据文件统计
                数据文件 = self.数据处理器.获取所有数据文件()
                总商品数 = 0

                for 文件路径 in 数据文件:
                    数据 = self.数据处理器.加载数据(文件路径)
                    if '商品列表' in 数据:
                        总商品数 += len(数据['商品列表'])

                return jsonify({
                    'success': True,
                    'data': {
                        '数据文件数': len(数据文件),
                        '总商品数': 总商品数,
                        '卖家数量': len(self.配置管理器.获取所有卖家()),
                        '最后更新': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                })
            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/获取商品数据')
        def api_获取商品数据():
            try:
                卖家ID = request.args.get('卖家ID')
                页码 = int(request.args.get('页码', 1))
                每页数量 = int(request.args.get('每页数量', 1000))  # 增加每页数量，前端再分页

                所有数据 = []
                数据文件 = self.数据处理器.获取所有数据文件()

                for 文件路径 in 数据文件:
                    try:
                        数据 = self.数据处理器.加载数据(文件路径)
                        if '商品列表' in 数据 and isinstance(数据['商品列表'], list):
                            卖家信息 = 数据.get('卖家信息', {})
                            当前卖家ID = 卖家信息.get('卖家ID', '')
                            当前卖家名称 = 卖家信息.get('卖家名称', '未知')

                            if not 卖家ID or 当前卖家ID == 卖家ID:
                                for 商品 in 数据['商品列表']:
                                    # 统一数据格式
                                    商品数据 = {
                                        '商品ID': 商品.get('商品ID', ''),
                                        '商品标题': 商品.get('商品标题', ''),
                                        '卖家ID': 当前卖家ID,
                                        '卖家名称': 当前卖家名称,
                                        '想要人数': 商品.get('想要人数', 0),
                                        '游戏名称': 商品.get('游戏名称', ''),
                                        '首次采集时间': 商品.get('首次采集时间', ''),
                                        '最近采集时间': 商品.get('最近采集时间', '')
                                    }
                                    所有数据.append(商品数据)
                    except Exception as e:
                        print(f"处理数据文件 {文件路径} 时出错: {e}")
                        continue

                return jsonify({
                    'success': True,
                    'data': 所有数据,
                    'total': len(所有数据)
                })
            except Exception as e:
                print(f"获取商品数据API错误: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/获取新上架商品')
        def api_获取新上架商品():
            try:
                时间范围 = request.args.get('时间范围', 'today')
                卖家ID = request.args.get('卖家ID')

                # 从新上架商品文件中读取数据
                新商品文件 = os.path.join(self.数据处理器.新上架目录, "新上架游戏.txt")
                新商品列表 = []

                if os.path.exists(新商品文件):
                    try:
                        with open(新商品文件, 'r', encoding='utf-8') as f:
                            lines = f.readlines()

                        # 解析新上架商品文件
                        for line in lines:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                # 解析格式：商品标题 [想要人数: X]
                                想要人数 = 0
                                商品标题 = line

                                if '[想要人数:' in line:
                                    parts = line.split('[想要人数:')
                                    商品标题 = parts[0].strip()
                                    if len(parts) > 1:
                                        try:
                                            想要人数_str = parts[1].split(']')[0].strip()
                                            想要人数 = int(想要人数_str)
                                        except:
                                            想要人数 = 0

                                # 创建商品数据
                                商品数据 = {
                                    '商品ID': f"new_{hash(商品标题) % 1000000}",
                                    '商品标题': 商品标题,
                                    '卖家ID': '未知',
                                    '卖家名称': '未知',
                                    '想要人数': 想要人数,
                                    '游戏名称': '',
                                    '上架时间': datetime.now().isoformat(),
                                    '采集时间': datetime.now().isoformat()
                                }
                                新商品列表.append(商品数据)

                    except Exception as e:
                        print(f"读取新上架商品文件失败: {e}")

                # 根据卖家筛选
                if 卖家ID and 卖家ID != '未知':
                    新商品列表 = [商品 for 商品 in 新商品列表 if 商品.get('卖家ID') == 卖家ID]

                return jsonify({
                    'success': True,
                    'data': 新商品列表
                })
            except Exception as e:
                print(f"获取新上架商品API错误: {e}")
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/Cookie管理', methods=['GET', 'POST'])
        def api_Cookie管理():
            if request.method == 'GET':
                try:
                    cookie = self.cookie管理器.获取Cookie()
                    有效性 = self.cookie管理器.验证Cookie有效性(cookie) if cookie else False

                    return jsonify({
                        'success': True,
                        'data': {
                            'has_cookie': bool(cookie),
                            'cookie_valid': 有效性,
                            'cookie_length': len(cookie) if cookie else 0
                        }
                    })
                except Exception as e:
                    return jsonify({'success': False, 'error': str(e)})

            elif request.method == 'POST':
                try:
                    data = request.get_json()
                    操作 = data.get('操作')

                    if 操作 == '保存':
                        cookie = data.get('cookie', '')
                        if self.cookie管理器.保存Cookie(cookie):
                            return jsonify({'success': True, 'message': 'Cookie保存成功'})
                        else:
                            return jsonify({'success': False, 'error': 'Cookie保存失败'})

                    elif 操作 == '自动获取':
                        new_cookie = self.cookie管理器.智能获取Cookie()
                        if new_cookie:
                            return jsonify({'success': True, 'message': 'Cookie自动获取成功', 'cookie': new_cookie})
                        else:
                            return jsonify({'success': False, 'error': 'Cookie自动获取失败'})

                    elif 操作 == '验证':
                        cookie = self.cookie管理器.获取Cookie()
                        有效性 = self.cookie管理器.验证Cookie有效性(cookie)
                        return jsonify({'success': True, 'valid': 有效性})

                    else:
                        return jsonify({'success': False, 'error': '未知操作'})

                except Exception as e:
                    return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/AI提纯', methods=['POST'])
        def api_AI提纯():
            try:
                data = request.get_json()
                卖家ID = data.get('卖家ID')
                批次大小 = data.get('批次大小', 20)
                只处理未提纯 = data.get('只处理未提纯', True)

                if not 卖家ID:
                    return jsonify({'success': False, 'error': '请选择卖家'})

                # 获取卖家商品数据
                数据文件 = self.数据处理器.获取所有数据文件()
                商品标题列表 = []

                for 文件路径 in 数据文件:
                    数据 = self.数据处理器.加载数据(文件路径)
                    卖家信息 = 数据.get('卖家信息', {})

                    if 卖家信息.get('卖家ID') == 卖家ID:
                        for 商品 in 数据.get('商品列表', []):
                            if 只处理未提纯 and '游戏名称' in 商品:
                                continue
                            商品标题列表.append(商品.get('商品标题', ''))

                if not 商品标题列表:
                    return jsonify({'success': False, 'error': '没有需要提纯的商品'})

                # 在新线程中执行AI提纯
                提纯线程 = threading.Thread(
                    target=self._执行AI提纯,
                    args=(商品标题列表, 批次大小)
                )
                提纯线程.daemon = True
                提纯线程.start()

                return jsonify({'success': True, 'message': 'AI提纯已开始'})

            except Exception as e:
                return jsonify({'success': False, 'error': str(e)})

        @self.app.route('/api/系统配置', methods=['GET', 'POST'])
        def api_系统配置():
            if request.method == 'GET':
                try:
                    系统设置 = self.配置管理器.获取系统设置()
                    默认设置 = self.配置管理器.获取默认设置()

                    return jsonify({
                        'success': True,
                        'data': {
                            '系统设置': 系统设置,
                            '默认设置': 默认设置
                        }
                    })
                except Exception as e:
                    return jsonify({'success': False, 'error': str(e)})

            elif request.method == 'POST':
                try:
                    data = request.get_json()
                    配置类型 = data.get('类型')

                    if 配置类型 == '系统设置':
                        # 更新系统设置
                        # 这里需要在配置管理器中添加更新系统设置的方法
                        return jsonify({'success': True, 'message': '系统配置保存成功'})

                    elif 配置类型 == '默认设置':
                        设置 = data.get('设置', {})
                        if self.配置管理器.更新默认设置(
                            设置.get('默认卖家ID'),
                            设置.get('默认卖家名称'),
                            设置.get('默认分组ID'),
                            设置.get('默认分组名称')
                        ):
                            return jsonify({'success': True, 'message': '默认设置保存成功'})
                        else:
                            return jsonify({'success': False, 'error': '默认设置保存失败'})

                    else:
                        return jsonify({'success': False, 'error': '未知配置类型'})

                except Exception as e:
                    return jsonify({'success': False, 'error': str(e)})

    def 注册Socket事件(self):
        """注册SocketIO事件处理器"""
        
        @self.socketio.on('connect')
        def handle_connect():
            print('客户端已连接')
            emit('connected', {'message': '连接成功'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            print('客户端已断开连接')
    
    def _执行采集(self, 卖家ID: str, 页数: int, 只采集有想要: bool, 只保存ID: bool):
        """执行数据采集（在独立线程中运行）"""
        try:
            self.采集状态 = "运行中"
            self.当前卖家ID = 卖家ID
            self.采集结果 = []
            
            self.发送实时消息(f"开始采集卖家 {卖家ID} 的商品数据...")
            
            采集结果 = []
            当前页码 = 1
            最大页数 = 页数 if 页数 > 0 else float('inf')
            连续失败次数 = 0
            最大连续失败次数 = 5
            
            while 当前页码 <= 最大页数 and self.采集状态 == "运行中":
                结果 = self.数据采集器.爬取卖家页面(当前页码, 卖家ID)
                
                if isinstance(结果, str):
                    if 结果 == "NO_MORE_DATA":
                        self.发送实时消息(f"已到达最后一页，共采集 {当前页码-1} 页")
                        break
                    elif 结果 in ["COOKIE_EXPIRED", "COOKIE_ERROR"]:
                        self.发送实时消息("Cookie已过期或无效，请更新Cookie", "error")
                        break
                    elif 结果 == "LIMIT_REACHED":
                        self.发送实时消息(f"已达到最大可查看页数限制，共采集 {当前页码-1} 页")
                        break
                    else:
                        连续失败次数 += 1
                        self.发送实时消息(f"第 {当前页码} 页采集失败: {结果} (连续失败: {连续失败次数})", "warning")
                        if 连续失败次数 >= 最大连续失败次数:
                            self.发送实时消息(f"连续失败次数过多({连续失败次数})，停止采集", "error")
                            break
                        当前页码 += 1
                        continue
                else:
                    连续失败次数 = 0
                    
                    if 只采集有想要:
                        结果 = [商品 for 商品 in 结果 if 'want_count' in 商品]
                    
                    采集结果.extend(结果)
                    self.发送实时消息(f"已采集第 {当前页码} 页，当前共有 {len(采集结果)} 个商品")
                    当前页码 += 1
                    time.sleep(0.5)
            
            if len(采集结果) > 0:
                文件路径, 新上架商品 = self.数据处理器.保存数据(采集结果, 卖家ID)
                self.发送实时消息(f"商品数据已保存到: {文件路径}")
                self.发送实时消息(f"采集完成，共采集到 {len(采集结果)} 个商品，其中新上架 {len(新上架商品)} 个商品", "success")
                
                if 新上架商品:
                    for 商品 in 新上架商品:
                        商品['卖家ID'] = 卖家ID
                        商品['卖家名称'] = self.配置管理器.获取卖家名称(卖家ID)
                        self.所有新上架商品.append(商品)
            else:
                self.发送实时消息("采集完成，但未获取到任何商品数据", "warning")
                
        except Exception as e:
            self.发送实时消息(f"采集过程中出现错误: {e}", "error")
        finally:
            if not self.批量采集中:
                self.采集状态 = "空闲"
                self.发送实时消息("采集任务结束", "info")

    def _执行AI提纯(self, 商品标题列表: list, 批次大小: int = 20):
        """执行AI提纯（在独立线程中运行）"""
        try:
            self.发送实时消息(f"开始AI提纯，共 {len(商品标题列表)} 个商品标题...")

            提纯结果 = self.ai提纯器.提纯游戏名称(商品标题列表)

            if 提纯结果:
                成功数量 = len([r for r in 提纯结果 if r.get('游戏名称') != '未知'])
                self.发送实时消息(f"AI提纯完成，成功提纯 {成功数量} 个商品", "success")
            else:
                self.发送实时消息("AI提纯失败，请检查API配置", "error")

        except Exception as e:
            self.发送实时消息(f"AI提纯过程中出现错误: {e}", "error")

    def 运行(self, host='127.0.0.1', port=5000, debug=False):
        """运行Web应用"""
        print(f"闲鱼采集工具 WEB版本启动中...")
        print(f"访问地址: http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)

if __name__ == '__main__':
    web_app = 闲鱼采集Web应用()
    web_app.运行(debug=True)
