{% extends "基础模板.html" %}

{% block title %}数据采集 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- 卖家管理区域 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-users me-2"></i>
          卖家管理
        </h5>
      </div>
      <div class="card-body">
        <!-- 添加卖家表单 -->
        <div class="row mb-3">
          <div class="col-md-3">
            <label for="seller-id-input" class="form-label">卖家ID</label>
            <input type="text" class="form-control" id="seller-id-input" placeholder="输入卖家ID">
          </div>
          <div class="col-md-3">
            <label for="seller-name-input" class="form-label">卖家名称</label>
            <input type="text" class="form-control" id="seller-name-input" placeholder="输入卖家名称">
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-primary d-block" onclick="添加卖家()">
              <i class="fas fa-plus me-1"></i>添加卖家
            </button>
          </div>
          <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <button type="button" class="btn btn-success d-block" onclick="刷新卖家列表()">
              <i class="fas fa-refresh me-1"></i>刷新列表
            </button>
          </div>
        </div>

        <!-- 卖家列表 -->
        <div class="table-responsive">
          <table class="table table-hover">
            <thead class="table-light">
              <tr>
                <th>
                  <input type="checkbox" id="select-all-sellers" onchange="全选卖家(this.checked)">
                </th>
                <th>卖家名称</th>
                <th>卖家ID</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="seller-list">
              <!-- 卖家列表将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 采集设置区域 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-cog me-2"></i>
          采集设置
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="page-count" class="form-label">采集页数</label>
          <input type="number" class="form-control" id="page-count" value="0" min="0" max="100">
          <div class="form-text">设置为0表示采集全部页面</div>
        </div>
        
        <div class="mb-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="only-wanted">
            <label class="form-check-label" for="only-wanted">
              只采集有想要人数的商品
            </label>
          </div>
        </div>
        
        <div class="mb-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="only-save-id">
            <label class="form-check-label" for="only-save-id">
              只保存商品ID
            </label>
          </div>
        </div>

        <div class="mb-3">
          <label for="thread-count" class="form-label">最大线程数</label>
          <input type="number" class="form-control" id="thread-count" value="5" min="1" max="10">
          <div class="form-text">批量采集时的并发线程数</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 采集控制区域 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-play me-2"></i>
          采集控制
        </h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <button type="button" class="btn btn-primary btn-lg" id="start-single-collection" onclick="开始单个采集()">
            <i class="fas fa-download me-2"></i>
            采集选中卖家
          </button>
          
          <button type="button" class="btn btn-success btn-lg" id="start-batch-collection" onclick="开始批量采集()">
            <i class="fas fa-download me-2"></i>
            批量采集选中卖家
          </button>
          
          <button type="button" class="btn btn-info btn-lg" id="start-all-collection" onclick="一键采集全部()">
            <i class="fas fa-magic me-2"></i>
            一键采集全部卖家
          </button>
          
          <button type="button" class="btn btn-danger btn-lg" id="stop-collection" onclick="停止采集()" disabled>
            <i class="fas fa-stop me-2"></i>
            停止采集
          </button>
        </div>

        <!-- 采集状态显示 -->
        <div class="mt-3">
          <div class="alert alert-info" id="collection-status-alert">
            <i class="fas fa-info-circle me-2"></i>
            <span id="collection-status-text">系统空闲，等待开始采集</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 实时日志区域 -->
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-terminal me-2"></i>
          实时日志
        </h5>
        <div>
          <button type="button" class="btn btn-sm btn-outline-secondary" onclick="清空日志()">
            <i class="fas fa-trash me-1"></i>清空日志
          </button>
          <button type="button" class="btn btn-sm btn-outline-primary" onclick="打开输出文件夹()">
            <i class="fas fa-folder-open me-1"></i>打开输出文件夹
          </button>
        </div>
      </div>
      <div class="card-body">
        <div id="log-container" class="bg-dark text-light p-3 rounded" style="height: 400px; overflow-y: auto; font-family: 'Courier New', monospace;">
          <div class="log-entry">
            <span class="text-success">[系统]</span> 
            <span class="text-muted">[{{ moment().format('YYYY-MM-DD HH:mm:ss') }}]</span> 
            系统已启动，等待用户操作...
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedSellers = new Set();
let isCollecting = false;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  加载卖家列表();
  初始化Socket连接();
});

function 加载卖家列表() {
  fetch('/api/卖家列表')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        显示卖家列表(data.data);
      } else {
        显示错误消息('加载卖家列表失败: ' + data.error);
      }
    })
    .catch(error => {
      console.error('加载卖家列表失败:', error);
      显示错误消息('网络错误，无法加载卖家列表');
    });
}

function 显示卖家列表(sellers) {
  const tbody = document.getElementById('seller-list');
  tbody.innerHTML = '';
  
  if (sellers.length === 0) {
    tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无卖家，请先添加卖家</td></tr>';
    return;
  }
  
  sellers.forEach(seller => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>
        <input type="checkbox" class="seller-checkbox" value="${seller.卖家ID}" 
               onchange="选择卖家('${seller.卖家ID}', this.checked)">
      </td>
      <td>${seller.卖家名称}</td>
      <td>${seller.卖家ID}</td>
      <td>
        <button class="btn btn-sm btn-primary me-1" onclick="单独采集('${seller.卖家ID}')">
          <i class="fas fa-download"></i>
        </button>
        <button class="btn btn-sm btn-danger" onclick="删除卖家('${seller.卖家ID}')">
          <i class="fas fa-trash"></i>
        </button>
      </td>
    `;
    tbody.appendChild(row);
  });
}

function 添加卖家() {
  const sellerId = document.getElementById('seller-id-input').value.trim();
  const sellerName = document.getElementById('seller-name-input').value.trim();
  
  if (!sellerId || !sellerName) {
    显示错误消息('请输入卖家ID和名称');
    return;
  }
  
  fetch('/api/添加卖家', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      卖家ID: sellerId,
      卖家名称: sellerName
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      显示成功消息('卖家添加成功');
      document.getElementById('seller-id-input').value = '';
      document.getElementById('seller-name-input').value = '';
      加载卖家列表();
    } else {
      显示错误消息('添加卖家失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('添加卖家失败:', error);
    显示错误消息('网络错误，添加卖家失败');
  });
}

function 删除卖家(sellerId) {
  if (!confirm('确定要删除这个卖家吗？')) {
    return;
  }
  
  fetch('/api/删除卖家', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      卖家ID: sellerId
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      显示成功消息('卖家删除成功');
      加载卖家列表();
    } else {
      显示错误消息('删除卖家失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('删除卖家失败:', error);
    显示错误消息('网络错误，删除卖家失败');
  });
}

function 选择卖家(sellerId, checked) {
  if (checked) {
    selectedSellers.add(sellerId);
  } else {
    selectedSellers.delete(sellerId);
  }
  
  // 更新全选状态
  const allCheckboxes = document.querySelectorAll('.seller-checkbox');
  const checkedCheckboxes = document.querySelectorAll('.seller-checkbox:checked');
  const selectAllCheckbox = document.getElementById('select-all-sellers');
  
  if (checkedCheckboxes.length === 0) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = false;
  } else if (checkedCheckboxes.length === allCheckboxes.length) {
    selectAllCheckbox.indeterminate = false;
    selectAllCheckbox.checked = true;
  } else {
    selectAllCheckbox.indeterminate = true;
  }
}

function 全选卖家(checked) {
  const checkboxes = document.querySelectorAll('.seller-checkbox');
  selectedSellers.clear();
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = checked;
    if (checked) {
      selectedSellers.add(checkbox.value);
    }
  });
}

function 开始单个采集() {
  if (selectedSellers.size === 0) {
    显示错误消息('请先选择要采集的卖家');
    return;
  }
  
  if (selectedSellers.size > 1) {
    显示错误消息('单个采集只能选择一个卖家');
    return;
  }
  
  const sellerId = Array.from(selectedSellers)[0];
  开始采集([sellerId]);
}

function 开始批量采集() {
  if (selectedSellers.size === 0) {
    显示错误消息('请先选择要采集的卖家');
    return;
  }
  
  开始采集(Array.from(selectedSellers));
}

function 一键采集全部() {
  // 获取所有卖家ID
  const checkboxes = document.querySelectorAll('.seller-checkbox');
  const allSellerIds = Array.from(checkboxes).map(cb => cb.value);
  
  if (allSellerIds.length === 0) {
    显示错误消息('没有可采集的卖家');
    return;
  }
  
  开始采集(allSellerIds);
}

function 开始采集(sellerIds) {
  if (isCollecting) {
    显示错误消息('采集正在进行中，请等待完成');
    return;
  }
  
  const pageCount = parseInt(document.getElementById('page-count').value) || 0;
  const onlyWanted = document.getElementById('only-wanted').checked;
  const onlySaveId = document.getElementById('only-save-id').checked;
  
  // 如果是多个卖家，使用批量采集
  if (sellerIds.length > 1) {
    // TODO: 实现批量采集API
    显示错误消息('批量采集功能正在开发中...');
    return;
  }
  
  fetch('/api/开始采集', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      卖家ID: sellerIds[0],
      页数: pageCount,
      只采集有想要: onlyWanted,
      只保存ID: onlySaveId
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      设置采集状态(true);
      显示成功消息('采集已开始');
    } else {
      显示错误消息('开始采集失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('开始采集失败:', error);
    显示错误消息('网络错误，开始采集失败');
  });
}

function 停止采集() {
  fetch('/api/停止采集', {
    method: 'POST'
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      设置采集状态(false);
      显示成功消息('采集已停止');
    } else {
      显示错误消息('停止采集失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('停止采集失败:', error);
    显示错误消息('网络错误，停止采集失败');
  });
}

function 设置采集状态(collecting) {
  isCollecting = collecting;
  
  // 更新按钮状态
  document.getElementById('start-single-collection').disabled = collecting;
  document.getElementById('start-batch-collection').disabled = collecting;
  document.getElementById('start-all-collection').disabled = collecting;
  document.getElementById('stop-collection').disabled = !collecting;
  
  // 更新状态显示
  const statusAlert = document.getElementById('collection-status-alert');
  const statusText = document.getElementById('collection-status-text');
  
  if (collecting) {
    statusAlert.className = 'alert alert-warning';
    statusText.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>采集进行中...';
  } else {
    statusAlert.className = 'alert alert-info';
    statusText.innerHTML = '<i class="fas fa-info-circle me-2"></i>系统空闲，等待开始采集';
  }
}

function 刷新卖家列表() {
  加载卖家列表();
}

function 清空日志() {
  const logContainer = document.getElementById('log-container');
  logContainer.innerHTML = '<div class="log-entry"><span class="text-success">[系统]</span> <span class="text-muted">[' + 
    new Date().toLocaleString() + ']</span> 日志已清空</div>';
}

function 打开输出文件夹() {
  显示信息消息('输出文件夹功能需要在桌面环境中使用');
}

function 单独采集(sellerId) {
  selectedSellers.clear();
  selectedSellers.add(sellerId);
  
  // 更新复选框状态
  document.querySelectorAll('.seller-checkbox').forEach(cb => {
    cb.checked = cb.value === sellerId;
  });
  
  开始采集([sellerId]);
}

// Socket.IO 连接和消息处理
function 初始化Socket连接() {
  const socket = io();
  
  socket.on('connect', function() {
    添加日志消息('系统', '已连接到服务器');
  });
  
  socket.on('disconnect', function() {
    添加日志消息('系统', '与服务器连接断开');
  });
  
  socket.on('status_update', function(data) {
    添加日志消息(data.type.toUpperCase(), data.message);
    
    // 如果收到采集完成的消息，更新状态
    if (data.message.includes('采集任务结束') || data.message.includes('采集完成')) {
      设置采集状态(false);
    }
  });
}

function 添加日志消息(type, message) {
  const logContainer = document.getElementById('log-container');
  const timestamp = new Date().toLocaleString();
  
  const typeColors = {
    '系统': 'text-success',
    'INFO': 'text-info',
    'WARNING': 'text-warning',
    'ERROR': 'text-danger',
    'SUCCESS': 'text-success'
  };
  
  const colorClass = typeColors[type.toUpperCase()] || 'text-light';
  
  const logEntry = document.createElement('div');
  logEntry.className = 'log-entry';
  logEntry.innerHTML = `
    <span class="${colorClass}">[${type}]</span> 
    <span class="text-muted">[${timestamp}]</span> 
    ${message}
  `;
  
  logContainer.appendChild(logEntry);
  logContainer.scrollTop = logContainer.scrollHeight;
}

// 消息显示函数
function 显示成功消息(message) {
  显示消息(message, 'success');
}

function 显示错误消息(message) {
  显示消息(message, 'danger');
}

function 显示信息消息(message) {
  显示消息(message, 'info');
}

function 显示消息(message, type) {
  // 这里可以使用Toast或其他消息显示组件
  // 暂时使用简单的alert
  if (type === 'danger') {
    alert('错误: ' + message);
  } else if (type === 'success') {
    alert('成功: ' + message);
  } else {
    alert('信息: ' + message);
  }
}
</script>
{% endblock %}
