import subprocess
import sys
from playwright.sync_api import sync_playwright
import os
import json
import time
import random
from typing import Optional
from .配置管理 import 配置管理器

class Cookie管理器:
    def __init__(self):
        self.配置 = None  # 延迟初始化
        self.配置目录 = None
        self.cookie配置文件 = None
        
    def 设置配置目录(self, 配置目录: str) -> None:
        """设置配置文件保存目录"""
        self.配置目录 = 配置目录
        self.cookie配置文件 = os.path.join(self.配置目录, 'cookie_config.json')

    def 设置配置管理器(self, 配置管理器) -> None:
        """设置配置管理器"""
        self.配置 = 配置管理器
        
    def 获取Cookie(self) -> str:
        """从配置文件获取Cookie"""
        try:
            if self.cookie配置文件 and os.path.exists(self.cookie配置文件):
                with open(self.cookie配置文件, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def 保存Cookie(self, cookie: str) -> bool:
        """保存Cookie到配置文件"""
        try:
            if not self.cookie配置文件:
                print("Cookie配置文件路径未设置")
                return False
                
            # 确保配置目录存在
            if not os.path.exists(self.配置目录):
                os.makedirs(self.配置目录)
                
            with open(self.cookie配置文件, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            print(f"Cookie已保存到: {self.cookie配置文件}")
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False
            
    def 刷新Cookie(self) -> Optional[str]:
        """自动获取新的Cookie"""
        print("正在启动浏览器自动获取Cookie...")

        try:
            with sync_playwright() as p:
                # 启动浏览器（使用chromium）
                browser = p.chromium.launch(
                    headless=False,  # 显示浏览器窗口，让用户看到过程
                    args=['--no-sandbox', '--disable-web-security']
                )

                # 创建新的浏览器上下文
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )

                # 创建新页面
                page = context.new_page()

                print("正在访问咸鱼网站...")
                # 访问咸鱼网站
                page.goto('https://www.goofish.com', wait_until='networkidle', timeout=30000)

                # 等待页面完全加载
                time.sleep(3)

                # 获取所有Cookie
                cookies = context.cookies()

                # 将Cookie转换为字符串格式
                cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])

                print(f"成功获取到Cookie，共{len(cookies)}个cookie项")

                # 关闭浏览器
                browser.close()

                if cookie_str:
                    # 保存新的Cookie
                    if self.保存Cookie(cookie_str):
                        print("Cookie已自动保存")
                        return cookie_str
                    else:
                        print("Cookie获取成功但保存失败")
                        return cookie_str
                else:
                    print("未能获取到有效的Cookie")
                    return None

        except Exception as e:
            print(f"自动获取Cookie失败: {e}")
            print("请尝试手动更新Cookie")
            return None

    def 智能获取Cookie(self) -> Optional[str]:
        """智能获取Cookie，专门获取关键的Cookie项"""
        print("正在智能获取Cookie...")

        try:
            with sync_playwright() as p:
                # 启动浏览器
                browser = p.chromium.launch(
                    headless=True,  # 后台运行，更快
                    args=['--no-sandbox', '--disable-web-security', '--disable-blink-features=AutomationControlled']
                )

                # 创建浏览器上下文，模拟真实用户
                context = browser.new_context(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    viewport={'width': 1920, 'height': 1080},
                    locale='zh-CN'
                )

                # 创建页面
                page = context.new_page()

                # 设置额外的请求头
                page.set_extra_http_headers({
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                })

                print("正在访问咸鱼网站获取Cookie...")
                # 访问咸鱼网站
                page.goto('https://www.goofish.com', wait_until='domcontentloaded', timeout=20000)

                # 等待关键Cookie生成
                time.sleep(2)

                # 尝试触发更多Cookie生成（访问一些API端点）
                try:
                    # 访问一个轻量级的API来触发更多Cookie
                    page.goto('https://www.goofish.com/api/common/config', wait_until='domcontentloaded', timeout=10000)
                    time.sleep(1)
                except:
                    pass  # 如果失败就忽略

                # 获取所有Cookie
                cookies = context.cookies()

                # 过滤出重要的Cookie项
                重要Cookie项 = ['_m_h5_tk', '_m_h5_tk_enc', 'tfstk', 'isg', 'xlly_s', 'mtop_partitioned_detect']

                cookie_dict = {}
                for cookie in cookies:
                    cookie_dict[cookie['name']] = cookie['value']

                # 构建Cookie字符串，优先包含重要项
                cookie_parts = []

                # 先添加重要的Cookie项
                for 重要项 in 重要Cookie项:
                    if 重要项 in cookie_dict:
                        cookie_parts.append(f"{重要项}={cookie_dict[重要项]}")

                # 再添加其他Cookie项
                for name, value in cookie_dict.items():
                    if name not in 重要Cookie项:
                        cookie_parts.append(f"{name}={value}")

                cookie_str = '; '.join(cookie_parts)

                print(f"成功获取到Cookie，共{len(cookies)}个cookie项")
                print(f"包含重要Cookie项: {[项 for 项 in 重要Cookie项 if 项 in cookie_dict]}")

                # 关闭浏览器
                browser.close()

                if cookie_str and any(重要项 in cookie_dict for 重要项 in 重要Cookie项):
                    # 保存新的Cookie
                    if self.保存Cookie(cookie_str):
                        print("Cookie已自动保存")
                        return cookie_str
                    else:
                        print("Cookie获取成功但保存失败")
                        return cookie_str
                else:
                    print("未能获取到包含关键项的有效Cookie")
                    return None

        except Exception as e:
            print(f"智能获取Cookie失败: {e}")
            # 如果智能获取失败，尝试普通获取
            print("尝试使用普通方式获取...")
            return self.刷新Cookie()

    def 验证Cookie有效性(self, cookie: str) -> bool:
        """验证Cookie是否有效"""
        if not cookie:
            return False

        # 检查是否包含关键的Cookie项
        关键项 = ['_m_h5_tk', '_m_h5_tk_enc']
        return any(项 in cookie for 项 in 关键项)
