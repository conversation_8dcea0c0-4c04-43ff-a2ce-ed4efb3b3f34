{% extends "基础模板.html" %}

{% block title %}系统设置 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- 系统配置 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-cog me-2"></i>
          系统配置
        </h5>
      </div>
      <div class="card-body">
        <form id="system-config-form">
          <div class="mb-3">
            <label for="user-agent" class="form-label">用户代理 (User-Agent)</label>
            <textarea class="form-control" id="user-agent" rows="3" 
                      placeholder="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"></textarea>
            <div class="form-text">浏览器标识，用于模拟真实用户访问</div>
          </div>
          
          <div class="mb-3">
            <label for="app-key" class="form-label">应用密钥 (App Key)</label>
            <input type="text" class="form-control" id="app-key" placeholder="12574478">
            <div class="form-text">淘宝API应用密钥</div>
          </div>
          
          <div class="mb-3">
            <label for="api-name" class="form-label">接口名称</label>
            <input type="text" class="form-control" id="api-name" 
                   placeholder="mtop.taobao.idle.shop.user.items">
            <div class="form-text">调用的API接口名称</div>
          </div>
          
          <div class="mb-3">
            <label for="base-url" class="form-label">基础URL</label>
            <input type="url" class="form-control" id="base-url" 
                   placeholder="https://h5api.m.taobao.com/h5/mtop.taobao.idle.shop.user.items/1.0/">
            <div class="form-text">API请求的基础地址</div>
          </div>
          
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>保存系统配置
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Cookie管理 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-cookie-bite me-2"></i>
          Cookie管理
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="cookie-input" class="form-label">Cookie内容</label>
          <textarea class="form-control" id="cookie-input" rows="4" 
                    placeholder="粘贴从浏览器复制的Cookie内容"></textarea>
          <div class="form-text">从浏览器开发者工具中复制完整的Cookie</div>
        </div>
        
        <div class="mb-3">
          <div class="row">
            <div class="col-6">
              <button type="button" class="btn btn-success w-100" onclick="保存Cookie()">
                <i class="fas fa-save me-1"></i>保存Cookie
              </button>
            </div>
            <div class="col-6">
              <button type="button" class="btn btn-info w-100" onclick="自动获取Cookie()">
                <i class="fas fa-magic me-1"></i>自动获取
              </button>
            </div>
          </div>
        </div>
        
        <div class="mb-3">
          <button type="button" class="btn btn-outline-primary w-100" onclick="验证Cookie()">
            <i class="fas fa-check-circle me-1"></i>验证Cookie有效性
          </button>
        </div>
        
        <!-- Cookie状态显示 -->
        <div id="cookie-status" class="alert alert-secondary">
          <i class="fas fa-info-circle me-2"></i>
          <span id="cookie-status-text">Cookie状态未知</span>
        </div>
      </div>
    </div>
  </div>

  <!-- API密钥管理 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-key me-2"></i>
          API密钥管理
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="openai-api-key" class="form-label">OpenAI API密钥</label>
          <div class="input-group">
            <input type="password" class="form-control" id="openai-api-key" 
                   placeholder="sk-...">
            <button class="btn btn-outline-secondary" type="button" onclick="切换密钥显示('openai-api-key')">
              <i class="fas fa-eye"></i>
            </button>
          </div>
          <div class="form-text">用于AI提纯功能的OpenAI API密钥</div>
        </div>
        
        <div class="mb-3">
          <button type="button" class="btn btn-outline-info w-100" onclick="测试API连接()">
            <i class="fas fa-plug me-1"></i>测试API连接
          </button>
        </div>
        
        <div class="mb-3">
          <button type="button" class="btn btn-primary w-100" onclick="保存API密钥()">
            <i class="fas fa-save me-1"></i>保存API密钥
          </button>
        </div>
        
        <!-- API状态显示 -->
        <div id="api-status" class="alert alert-secondary">
          <i class="fas fa-info-circle me-2"></i>
          <span id="api-status-text">API状态未知</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 默认设置 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-sliders-h me-2"></i>
          默认设置
        </h5>
      </div>
      <div class="card-body">
        <form id="default-settings-form">
          <div class="mb-3">
            <label for="default-seller-id" class="form-label">默认卖家ID</label>
            <input type="text" class="form-control" id="default-seller-id" placeholder="3800476293">
          </div>
          
          <div class="mb-3">
            <label for="default-seller-name" class="form-label">默认卖家名称</label>
            <input type="text" class="form-control" id="default-seller-name" placeholder="小茶">
          </div>
          
          <div class="mb-3">
            <label for="default-group-id" class="form-label">默认分组ID</label>
            <input type="text" class="form-control" id="default-group-id" placeholder="51959993">
          </div>
          
          <div class="mb-3">
            <label for="default-group-name" class="form-label">默认分组名称</label>
            <input type="text" class="form-control" id="default-group-name" placeholder="综合">
          </div>
          
          <div class="d-grid">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save me-1"></i>保存默认设置
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- 数据管理 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-database me-2"></i>
          数据管理
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 mb-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-primary" id="data-files-count">-</h4>
                <p class="text-muted mb-2">数据文件</p>
                <button class="btn btn-sm btn-outline-primary" onclick="刷新数据统计()">
                  <i class="fas fa-refresh"></i>
                </button>
              </div>
            </div>
          </div>
          
          <div class="col-md-3 mb-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-success" id="total-products-count">-</h4>
                <p class="text-muted mb-2">总商品数</p>
                <button class="btn btn-sm btn-outline-success" onclick="查看数据详情()">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </div>
          </div>
          
          <div class="col-md-3 mb-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-info" id="data-size">-</h4>
                <p class="text-muted mb-2">数据大小</p>
                <button class="btn btn-sm btn-outline-info" onclick="清理数据()">
                  <i class="fas fa-broom"></i>
                </button>
              </div>
            </div>
          </div>
          
          <div class="col-md-3 mb-3">
            <div class="card text-center">
              <div class="card-body">
                <h4 class="text-warning" id="last-backup">-</h4>
                <p class="text-muted mb-2">最后备份</p>
                <button class="btn btn-sm btn-outline-warning" onclick="备份数据()">
                  <i class="fas fa-download"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row mt-3">
          <div class="col-md-4">
            <button type="button" class="btn btn-success w-100" onclick="导出全部数据()">
              <i class="fas fa-download me-1"></i>导出全部数据
            </button>
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-warning w-100" onclick="导入数据()">
              <i class="fas fa-upload me-1"></i>导入数据
            </button>
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-danger w-100" onclick="清空全部数据()">
              <i class="fas fa-trash me-1"></i>清空全部数据
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 系统信息 -->
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-info-circle me-2"></i>
          系统信息
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-sm">
              <tr>
                <td>应用版本</td>
                <td><span class="badge bg-primary">WEB v1.0.0</span></td>
              </tr>
              <tr>
                <td>Python版本</td>
                <td id="python-version">-</td>
              </tr>
              <tr>
                <td>Flask版本</td>
                <td id="flask-version">-</td>
              </tr>
              <tr>
                <td>运行时间</td>
                <td id="uptime">-</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-sm">
              <tr>
                <td>配置目录</td>
                <td><code id="config-dir">-</code></td>
              </tr>
              <tr>
                <td>数据目录</td>
                <td><code id="data-dir">-</code></td>
              </tr>
              <tr>
                <td>日志目录</td>
                <td><code id="log-dir">-</code></td>
              </tr>
              <tr>
                <td>最后启动</td>
                <td id="last-start">-</td>
              </tr>
            </table>
          </div>
        </div>
        
        <div class="mt-3">
          <button type="button" class="btn btn-outline-info me-2" onclick="查看日志()">
            <i class="fas fa-file-alt me-1"></i>查看日志
          </button>
          <button type="button" class="btn btn-outline-secondary me-2" onclick="重启应用()">
            <i class="fas fa-redo me-1"></i>重启应用
          </button>
          <button type="button" class="btn btn-outline-primary" onclick="检查更新()">
            <i class="fas fa-sync me-1"></i>检查更新
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 文件上传模态框 -->
<div class="modal fade" id="import-data-modal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">导入数据</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="import-file" class="form-label">选择数据文件</label>
          <input type="file" class="form-control" id="import-file" accept=".json,.csv">
          <div class="form-text">支持JSON和CSV格式的数据文件</div>
        </div>
        <div class="mb-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="merge-data">
            <label class="form-check-label" for="merge-data">
              合并到现有数据（不选择则覆盖）
            </label>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="执行导入数据()">
          <i class="fas fa-upload me-1"></i>开始导入
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  加载系统配置();
  加载Cookie状态();
  加载API状态();
  加载默认设置();
  加载系统信息();
  刷新数据统计();
  
  // 绑定表单提交事件
  document.getElementById('system-config-form').addEventListener('submit', 保存系统配置);
  document.getElementById('default-settings-form').addEventListener('submit', 保存默认设置);
});

function 加载系统配置() {
  // 这里应该调用API获取系统配置
  // 暂时使用默认值
  document.getElementById('user-agent').value = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  document.getElementById('app-key').value = '12574478';
  document.getElementById('api-name').value = 'mtop.taobao.idle.shop.user.items';
  document.getElementById('base-url').value = 'https://h5api.m.taobao.com/h5/mtop.taobao.idle.shop.user.items/1.0/';
}

function 保存系统配置(event) {
  event.preventDefault();
  
  const config = {
    用户代理: document.getElementById('user-agent').value,
    应用密钥: document.getElementById('app-key').value,
    接口名称: document.getElementById('api-name').value,
    基础URL: document.getElementById('base-url').value
  };
  
  // 这里应该调用API保存配置
  显示通知('系统配置保存成功', 'success');
}

function 加载Cookie状态() {
  // 这里应该调用API获取Cookie状态
  更新Cookie状态('未知', 'secondary');
}

function 保存Cookie() {
  const cookie = document.getElementById('cookie-input').value.trim();
  if (!cookie) {
    显示通知('请输入Cookie内容', 'warning');
    return;
  }
  
  // 这里应该调用API保存Cookie
  显示通知('Cookie保存成功', 'success');
  更新Cookie状态('已保存', 'success');
}

function 自动获取Cookie() {
  更新Cookie状态('正在获取...', 'info');
  
  // 这里应该调用API自动获取Cookie
  setTimeout(() => {
    // 模拟获取结果
    const success = Math.random() > 0.3;
    if (success) {
      更新Cookie状态('自动获取成功', 'success');
      显示通知('Cookie自动获取成功', 'success');
    } else {
      更新Cookie状态('自动获取失败', 'danger');
      显示通知('Cookie自动获取失败，请手动设置', 'danger');
    }
  }, 3000);
}

function 验证Cookie() {
  更新Cookie状态('验证中...', 'info');
  
  // 这里应该调用API验证Cookie
  setTimeout(() => {
    // 模拟验证结果
    const valid = Math.random() > 0.4;
    if (valid) {
      更新Cookie状态('Cookie有效', 'success');
      显示通知('Cookie验证通过', 'success');
    } else {
      更新Cookie状态('Cookie无效', 'danger');
      显示通知('Cookie验证失败，请更新Cookie', 'danger');
    }
  }, 2000);
}

function 更新Cookie状态(text, type) {
  const statusDiv = document.getElementById('cookie-status');
  const statusText = document.getElementById('cookie-status-text');
  
  const typeClasses = {
    'success': 'alert-success',
    'danger': 'alert-danger',
    'warning': 'alert-warning',
    'info': 'alert-info',
    'secondary': 'alert-secondary'
  };
  
  statusDiv.className = `alert ${typeClasses[type] || 'alert-secondary'}`;
  statusText.textContent = text;
}

function 加载API状态() {
  // 这里应该调用API获取API状态
  更新API状态('未知', 'secondary');
}

function 保存API密钥() {
  const apiKey = document.getElementById('openai-api-key').value.trim();
  if (!apiKey) {
    显示通知('请输入API密钥', 'warning');
    return;
  }
  
  // 这里应该调用API保存密钥
  显示通知('API密钥保存成功', 'success');
  更新API状态('已保存', 'success');
}

function 测试API连接() {
  更新API状态('测试中...', 'info');
  
  // 这里应该调用API测试连接
  setTimeout(() => {
    // 模拟测试结果
    const success = Math.random() > 0.3;
    if (success) {
      更新API状态('连接正常', 'success');
      显示通知('API连接测试成功', 'success');
    } else {
      更新API状态('连接失败', 'danger');
      显示通知('API连接测试失败，请检查密钥', 'danger');
    }
  }, 2000);
}

function 更新API状态(text, type) {
  const statusDiv = document.getElementById('api-status');
  const statusText = document.getElementById('api-status-text');
  
  const typeClasses = {
    'success': 'alert-success',
    'danger': 'alert-danger',
    'warning': 'alert-warning',
    'info': 'alert-info',
    'secondary': 'alert-secondary'
  };
  
  statusDiv.className = `alert ${typeClasses[type] || 'alert-secondary'}`;
  statusText.textContent = text;
}

function 切换密钥显示(inputId) {
  const input = document.getElementById(inputId);
  const button = input.nextElementSibling;
  const icon = button.querySelector('i');
  
  if (input.type === 'password') {
    input.type = 'text';
    icon.className = 'fas fa-eye-slash';
  } else {
    input.type = 'password';
    icon.className = 'fas fa-eye';
  }
}

function 加载默认设置() {
  // 这里应该调用API获取默认设置
  document.getElementById('default-seller-id').value = '3800476293';
  document.getElementById('default-seller-name').value = '小茶';
  document.getElementById('default-group-id').value = '51959993';
  document.getElementById('default-group-name').value = '综合';
}

function 保存默认设置(event) {
  event.preventDefault();
  
  const settings = {
    默认卖家ID: document.getElementById('default-seller-id').value,
    默认卖家名称: document.getElementById('default-seller-name').value,
    默认分组ID: document.getElementById('default-group-id').value,
    默认分组名称: document.getElementById('default-group-name').value
  };
  
  // 这里应该调用API保存设置
  显示通知('默认设置保存成功', 'success');
}

function 刷新数据统计() {
  // 这里应该调用API获取数据统计
  document.getElementById('data-files-count').textContent = '5';
  document.getElementById('total-products-count').textContent = '1,234';
  document.getElementById('data-size').textContent = '2.5MB';
  document.getElementById('last-backup').textContent = '从未';
}

function 查看数据详情() {
  // 跳转到数据查看页面
  window.location.href = '/数据查看';
}

function 清理数据() {
  if (confirm('确定要清理临时数据吗？这将删除缓存文件和日志文件。')) {
    显示通知('数据清理完成', 'success');
    刷新数据统计();
  }
}

function 备份数据() {
  显示通知('正在备份数据...', 'info');
  
  // 这里应该调用API备份数据
  setTimeout(() => {
    显示通知('数据备份完成', 'success');
    document.getElementById('last-backup').textContent = new Date().toLocaleString();
  }, 2000);
}

function 导出全部数据() {
  显示通知('正在导出数据...', 'info');
  
  // 这里应该调用API导出数据
  setTimeout(() => {
    // 模拟下载文件
    const filename = `闲鱼数据备份_${new Date().toISOString().slice(0, 10)}.zip`;
    显示通知(`数据导出完成: ${filename}`, 'success');
  }, 3000);
}

function 导入数据() {
  const modal = new bootstrap.Modal(document.getElementById('import-data-modal'));
  modal.show();
}

function 执行导入数据() {
  const fileInput = document.getElementById('import-file');
  const mergeData = document.getElementById('merge-data').checked;
  
  if (!fileInput.files.length) {
    显示通知('请选择要导入的文件', 'warning');
    return;
  }
  
  // 这里应该处理文件上传和导入
  显示通知('数据导入成功', 'success');
  
  const modal = bootstrap.Modal.getInstance(document.getElementById('import-data-modal'));
  modal.hide();
  
  刷新数据统计();
}

function 清空全部数据() {
  if (confirm('警告：这将删除所有采集的数据，此操作不可恢复！\n\n确定要继续吗？')) {
    if (confirm('最后确认：真的要删除所有数据吗？')) {
      显示通知('所有数据已清空', 'success');
      刷新数据统计();
    }
  }
}

function 加载系统信息() {
  // 这里应该调用API获取系统信息
  document.getElementById('python-version').textContent = 'Python 3.9.0';
  document.getElementById('flask-version').textContent = 'Flask 2.3.0';
  document.getElementById('uptime').textContent = '2小时15分钟';
  document.getElementById('config-dir').textContent = './配置';
  document.getElementById('data-dir').textContent = './数据';
  document.getElementById('log-dir').textContent = './日志';
  document.getElementById('last-start').textContent = new Date().toLocaleString();
}

function 查看日志() {
  // 这里可以打开日志查看页面或模态框
  显示通知('日志查看功能正在开发中...', 'info');
}

function 重启应用() {
  if (confirm('确定要重启应用吗？这将中断当前的所有操作。')) {
    显示通知('应用正在重启...', 'info');
    // 这里应该调用API重启应用
  }
}

function 检查更新() {
  显示通知('正在检查更新...', 'info');
  
  // 这里应该调用API检查更新
  setTimeout(() => {
    显示通知('当前已是最新版本', 'success');
  }, 2000);
}
</script>
{% endblock %}
