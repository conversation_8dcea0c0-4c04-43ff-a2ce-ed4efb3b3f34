# 闲鱼商品采集工具 - WEB版本

专业的闲鱼卖家商品数据采集与分析工具，基于Flask框架的Web版本。

## 功能特性

### 🚀 核心功能
- **数据采集**: 支持单个卖家和批量卖家的商品数据采集
- **实时监控**: 自动检测新上架商品，实时推送更新
- **AI提纯**: 使用OpenAI API从商品标题中提取准确的游戏名称
- **数据管理**: 完整的数据查看、筛选、搜索和导出功能
- **系统设置**: 灵活的配置管理和Cookie管理

### 🎯 主要特点
- **Web界面**: 现代化的响应式Web界面，支持多设备访问
- **实时通信**: 基于WebSocket的实时状态更新和日志显示
- **数据可视化**: 直观的统计图表和数据展示
- **批量操作**: 支持批量采集、批量导出等操作
- **智能筛选**: 多维度数据筛选和排序功能

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 安装浏览器驱动 (首次使用)
```bash
playwright install
```

### 3. 启动应用
```bash
python 启动.py
```

或使用Windows批处理文件：
```bash
启动WEB版本.bat
```

### 4. 访问应用
浏览器会自动打开 `http://127.0.0.1:5000`

## 使用说明

### 首次配置
1. 进入**系统设置**页面
2. 配置Cookie信息（必需）
3. 配置OpenAI API密钥（AI提纯功能需要）

### Cookie获取方法
1. 访问 `https://www.goofish.com` 并登录
2. 按F12打开开发者工具
3. 在Network标签中找到任意请求
4. 复制Request Headers中的Cookie值
5. 粘贴到系统设置页面

### 基本操作
1. **添加卖家**: 在数据采集页面添加要监控的卖家
2. **开始采集**: 选择卖家并设置采集参数
3. **查看数据**: 在数据查看页面管理采集的数据
4. **AI提纯**: 使用AI提取准确的游戏名称
5. **监控新品**: 查看最新上架的热门商品

## 技术栈

- **后端**: Python 3.8+, Flask, Flask-SocketIO
- **前端**: HTML5, CSS3, JavaScript, Bootstrap 5
- **数据存储**: JSON文件
- **浏览器自动化**: Playwright
- **AI服务**: OpenAI API

## 注意事项

1. 请遵守闲鱼平台的使用条款
2. 合理控制采集频率，避免对平台造成负担
3. 妥善保管API密钥和Cookie信息
4. 定期更新依赖包以确保安全性

## 许可证

本项目仅供学习和研究使用。
