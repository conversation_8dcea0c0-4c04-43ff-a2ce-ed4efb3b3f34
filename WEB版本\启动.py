#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品采集工具 WEB版本 - 启动脚本
"""

import os
import sys
import webbrowser
import time
import threading
from 主程序 import 闲鱼采集Web应用

def 检查依赖():
    """检查必要的依赖包是否已安装"""
    required_packages = [
        'flask',
        'flask_socketio',
        'requests',
        'playwright'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        print("\n如果是第一次使用playwright，还需要运行:")
        print("playwright install")
        return False
    
    return True

def 自动打开浏览器(host, port, delay=2):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        url = f"http://{host}:{port}"
        try:
            webbrowser.open(url)
            print(f"已自动打开浏览器: {url}")
        except Exception as e:
            print(f"无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()

def main():
    """主函数"""
    print("=" * 60)
    print("闲鱼商品采集工具 WEB版本")
    print("=" * 60)
    
    # 检查依赖
    print("正在检查依赖包...")
    if not 检查依赖():
        input("按回车键退出...")
        return
    
    print("依赖检查通过!")
    
    # 创建应用实例
    try:
        web_app = 闲鱼采集Web应用()
        print("应用初始化成功!")
    except Exception as e:
        print(f"应用初始化失败: {e}")
        input("按回车键退出...")
        return
    
    # 配置参数
    host = '127.0.0.1'
    port = 5000
    debug = False
    
    # 检查端口是否被占用
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex((host, port))
    sock.close()
    
    if result == 0:
        print(f"端口 {port} 已被占用，尝试使用其他端口...")
        for test_port in range(5001, 5010):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex((host, test_port))
            sock.close()
            if result != 0:
                port = test_port
                break
        else:
            print("无法找到可用端口，请检查网络设置")
            input("按回车键退出...")
            return
    
    print(f"服务器将在 http://{host}:{port} 启动")
    print("=" * 60)
    
    # 自动打开浏览器
    自动打开浏览器(host, port)
    
    try:
        # 启动应用
        web_app.运行(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n\n应用已停止")
    except Exception as e:
        print(f"\n应用运行出错: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
