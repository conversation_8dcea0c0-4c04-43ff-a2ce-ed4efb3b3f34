#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
闲鱼商品采集工具 WEB版本 - 测试脚本
用于测试各个模块的基本功能
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加modules目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

def 测试配置管理():
    """测试配置管理模块"""
    print("=" * 50)
    print("测试配置管理模块")
    print("=" * 50)
    
    try:
        from modules.配置管理 import 配置管理器
        
        # 创建配置管理器实例
        配置管理 = 配置管理器()
        配置管理.设置配置目录('./配置')
        
        # 测试获取系统设置
        系统设置 = 配置管理.获取系统设置()
        print(f"系统设置: {系统设置}")
        
        # 测试添加卖家
        if 配置管理.添加卖家('test123', '测试卖家'):
            print("✓ 添加卖家成功")
        else:
            print("✗ 添加卖家失败")
        
        # 测试获取卖家列表
        卖家列表 = 配置管理.获取所有卖家()
        print(f"卖家列表: {len(卖家列表)} 个卖家")
        
        # 测试删除卖家
        if 配置管理.删除卖家('test123'):
            print("✓ 删除卖家成功")
        else:
            print("✗ 删除卖家失败")
        
        print("配置管理模块测试完成")
        return True
        
    except Exception as e:
        print(f"配置管理模块测试失败: {e}")
        return False

def 测试Web应用():
    """测试Web应用主程序"""
    print("=" * 50)
    print("测试Web应用主程序")
    print("=" * 50)
    
    try:
        from 主程序 import 闲鱼采集Web应用
        
        # 创建应用实例
        web_app = 闲鱼采集Web应用()
        print("✓ Web应用初始化成功")
        
        # 测试文件夹结构
        必需目录 = ['数据', '配置', '日志', 'templates', 'static']
        for 目录 in 必需目录:
            if os.path.exists(目录):
                print(f"✓ {目录} 目录存在")
            else:
                print(f"✗ {目录} 目录不存在")
        
        print("Web应用测试完成")
        return True
        
    except Exception as e:
        print(f"Web应用测试失败: {e}")
        return False

def 运行所有测试():
    """运行所有测试"""
    print("开始运行闲鱼采集工具 WEB版本测试")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print()
    
    测试结果 = []
    
    # 运行各个测试
    测试结果.append(('配置管理', 测试配置管理()))
    测试结果.append(('Web应用', 测试Web应用()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    成功数量 = 0
    for 模块名, 结果 in 测试结果:
        状态 = "✓ 通过" if 结果 else "✗ 失败"
        print(f"{模块名:<15} {状态}")
        if 结果:
            成功数量 += 1
    
    print(f"\n总计: {成功数量}/{len(测试结果)} 个模块测试通过")
    
    if 成功数量 == len(测试结果):
        print("🎉 所有测试通过！WEB版本可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return 成功数量 == len(测试结果)

if __name__ == '__main__':
    try:
        运行所有测试()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    
    input("\n按回车键退出...")
