# 闲鱼商品采集工具 - 配置文件示例
# 请将此文件重命名为 闲鱼配置.ini 并修改相应配置

[系统设置]
用户代理 = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36
应用密钥 = 12574478
接口名称 = mtop.taobao.idle.shop.user.items
基础url = https://h5api.m.taobao.com/h5/mtop.taobao.idle.shop.user.items/1.0/

[默认设置]
默认卖家id = 3800476293
默认卖家名称 = 小茶
默认分组id = 51959993
默认分组名称 = 综合

# 卖家配置示例
# [卖家_3800476293]
# 卖家ID = 3800476293
# 卖家名称 = 小茶

# [卖家_1234567890]
# 卖家ID = 1234567890
# 卖家名称 = 游戏小店
