{% extends "基础模板.html" %}

{% block title %}新上架商品 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- 筛选和统计区域 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-star me-2"></i>
          新上架商品监控
        </h5>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-3">
            <label for="time-filter" class="form-label">时间范围</label>
            <select class="form-select" id="time-filter" onchange="筛选新商品()">
              <option value="today">今天</option>
              <option value="yesterday">昨天</option>
              <option value="week">本周</option>
              <option value="month">本月</option>
              <option value="all">全部</option>
            </select>
          </div>
          <div class="col-md-3">
            <label for="seller-filter" class="form-label">卖家筛选</label>
            <select class="form-select" id="seller-filter" onchange="筛选新商品()">
              <option value="">全部卖家</option>
              <!-- 卖家选项将通过JavaScript动态加载 -->
            </select>
          </div>
          <div class="col-md-3">
            <label for="want-filter" class="form-label">想要人数</label>
            <select class="form-select" id="want-filter" onchange="筛选新商品()">
              <option value="">全部</option>
              <option value="0">0人想要</option>
              <option value="1-5">1-5人</option>
              <option value="6-20">6-20人</option>
              <option value="21+">20人以上</option>
            </select>
          </div>
          <div class="col-md-3">
            <label for="sort-filter" class="form-label">排序方式</label>
            <select class="form-select" id="sort-filter" onchange="排序新商品()">
              <option value="time-desc">最新上架</option>
              <option value="want-desc">想要人数↓</option>
              <option value="want-asc">想要人数↑</option>
              <option value="title-asc">标题A-Z</option>
            </select>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="row text-center">
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h4 class="text-primary mb-1" id="total-new-products">0</h4>
              <small class="text-muted">新上架商品</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h4 class="text-success mb-1" id="wanted-new-products">0</h4>
              <small class="text-muted">有人想要</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h4 class="text-info mb-1" id="hot-new-products">0</h4>
              <small class="text-muted">热门商品(10+)</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h4 class="text-warning mb-1" id="filtered-count">0</h4>
              <small class="text-muted">筛选结果</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 热门商品快速预览 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h6 class="card-title mb-0">
          <i class="fas fa-fire me-2 text-danger"></i>
          热门新品 (想要人数≥10)
        </h6>
      </div>
      <div class="card-body">
        <div id="hot-products-carousel" class="carousel slide" data-bs-ride="carousel">
          <div class="carousel-inner" id="hot-products-container">
            <!-- 热门商品将通过JavaScript动态加载 -->
          </div>
          <button class="carousel-control-prev" type="button" data-bs-target="#hot-products-carousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#hot-products-carousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 新商品列表 -->
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-list me-2"></i>
          新上架商品列表
        </h5>
        <div>
          <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="导出新商品数据()">
            <i class="fas fa-download me-1"></i>导出数据
          </button>
          <button type="button" class="btn btn-sm btn-outline-success" onclick="刷新新商品()">
            <i class="fas fa-refresh me-1"></i>刷新数据
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- 加载状态 -->
        <div id="loading-indicator" class="text-center py-5" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-2 text-muted">正在加载新商品数据...</p>
        </div>

        <!-- 商品网格 -->
        <div id="products-grid" class="row">
          <!-- 商品卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 分页 -->
        <nav aria-label="商品分页" id="pagination-container" class="mt-4" style="display: none;">
          <ul class="pagination justify-content-center" id="pagination">
            <!-- 分页将通过JavaScript动态生成 -->
          </ul>
        </nav>

        <!-- 空数据提示 -->
        <div id="empty-data" class="text-center py-5" style="display: none;">
          <i class="fas fa-star fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">暂无新上架商品</h5>
          <p class="text-muted">请先进行数据采集，或调整筛选条件</p>
          <a href="{{ url_for('数据采集页面') }}" class="btn btn-primary">
            <i class="fas fa-download me-1"></i>开始采集
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 商品详情模态框 -->
<div class="modal fade" id="product-detail-modal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">商品详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="product-detail-content">
        <!-- 详情内容将通过JavaScript动态加载 -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" onclick="打开商品链接()">
          <i class="fas fa-external-link-alt me-1"></i>打开链接
        </button>
        <button type="button" class="btn btn-success" onclick="复制商品信息()">
          <i class="fas fa-copy me-1"></i>复制信息
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allNewProducts = [];
let filteredProducts = [];
let currentPage = 1;
let itemsPerPage = 12;
let currentProductDetail = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  加载新商品数据();
  初始化事件监听();
});

function 初始化事件监听() {
  // 监听状态更新
  document.addEventListener('statusUpdate', function(event) {
    const data = event.detail;
    if (data.message.includes('新上架') || data.message.includes('采集完成')) {
      // 有新商品时自动刷新
      setTimeout(() => {
        刷新新商品();
      }, 1000);
    }
  });
}

function 加载新商品数据() {
  显示加载状态(true);
  
  // 这里应该调用API获取新商品数据
  // 暂时使用模拟数据
  setTimeout(() => {
    模拟加载新商品();
    显示加载状态(false);
  }, 1000);
}

function 模拟加载新商品() {
  // 模拟新商品数据
  allNewProducts = [
    {
      商品ID: '123456789',
      商品标题: '王者荣耀 永久皮肤 限定皮肤 代练上分 五排',
      卖家ID: '3800476293',
      卖家名称: '小茶',
      想要人数: 25,
      游戏名称: '王者荣耀',
      上架时间: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
      采集时间: new Date().toISOString()
    },
    {
      商品ID: '987654321',
      商品标题: '原神 五星角色 满命满精 代练 胡桃 甘雨',
      卖家ID: '3800476293',
      卖家名称: '小茶',
      想要人数: 18,
      游戏名称: '原神',
      上架时间: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4小时前
      采集时间: new Date().toISOString()
    },
    {
      商品ID: '456789123',
      商品标题: '和平精英 时装 永久 稀有 绝版',
      卖家ID: '1234567890',
      卖家名称: '游戏小店',
      想要人数: 12,
      游戏名称: '和平精英',
      上架时间: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6小时前
      采集时间: new Date().toISOString()
    },
    {
      商品ID: '789123456',
      商品标题: '英雄联盟 皮肤 永久 限定 稀有',
      卖家ID: '1234567890',
      卖家名称: '游戏小店',
      想要人数: 8,
      游戏名称: '英雄联盟',
      上架时间: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8小时前
      采集时间: new Date().toISOString()
    }
  ];
  
  filteredProducts = [...allNewProducts];
  更新统计信息();
  显示新商品();
  加载卖家选项();
  显示热门商品();
}

function 显示加载状态(loading) {
  const loadingIndicator = document.getElementById('loading-indicator');
  const productsGrid = document.getElementById('products-grid');
  const emptyData = document.getElementById('empty-data');
  
  if (loading) {
    loadingIndicator.style.display = 'block';
    productsGrid.style.display = 'none';
    emptyData.style.display = 'none';
  } else {
    loadingIndicator.style.display = 'none';
    if (filteredProducts.length > 0) {
      productsGrid.style.display = 'flex';
      emptyData.style.display = 'none';
    } else {
      productsGrid.style.display = 'none';
      emptyData.style.display = 'block';
    }
  }
}

function 更新统计信息() {
  const totalNew = allNewProducts.length;
  const wantedNew = allNewProducts.filter(p => p.想要人数 > 0).length;
  const hotNew = allNewProducts.filter(p => p.想要人数 >= 10).length;
  const filteredCount = filteredProducts.length;
  
  document.getElementById('total-new-products').textContent = totalNew;
  document.getElementById('wanted-new-products').textContent = wantedNew;
  document.getElementById('hot-new-products').textContent = hotNew;
  document.getElementById('filtered-count').textContent = filteredCount;
}

function 显示新商品() {
  const grid = document.getElementById('products-grid');
  grid.innerHTML = '';
  
  // 分页处理
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageProducts = filteredProducts.slice(startIndex, endIndex);
  
  pageProducts.forEach(product => {
    const card = 创建商品卡片(product);
    grid.appendChild(card);
  });
  
  更新分页();
}

function 创建商品卡片(product) {
  const col = document.createElement('div');
  col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
  
  const wantBadgeClass = product.想要人数 >= 20 ? 'bg-danger' : 
                        product.想要人数 >= 10 ? 'bg-warning' : 
                        product.想要人数 > 0 ? 'bg-success' : 'bg-secondary';
  
  const timeAgo = 计算时间差(product.上架时间);
  
  col.innerHTML = `
    <div class="card h-100 shadow-sm product-card" onclick="查看商品详情('${product.商品ID}')">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-start mb-2">
          <span class="badge ${wantBadgeClass}">
            <i class="fas fa-heart me-1"></i>${product.想要人数}人想要
          </span>
          <small class="text-muted">${timeAgo}</small>
        </div>
        
        <h6 class="card-title text-truncate-2" title="${product.商品标题}">
          ${product.商品标题}
        </h6>
        
        <div class="mb-2">
          <small class="text-muted">
            <i class="fas fa-user me-1"></i>${product.卖家名称}
          </small>
        </div>
        
        <div class="mb-2">
          <span class="badge bg-primary">${product.游戏名称 || '未知游戏'}</span>
        </div>
        
        <div class="d-flex justify-content-between align-items-center">
          <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            ${new Date(product.上架时间).toLocaleString()}
          </small>
        </div>
      </div>
      
      <div class="card-footer bg-transparent">
        <div class="d-flex justify-content-between">
          <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); 复制商品ID('${product.商品ID}')">
            <i class="fas fa-copy"></i>
          </button>
          <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); 打开商品页面('${product.商品ID}')">
            <i class="fas fa-external-link-alt"></i>
          </button>
        </div>
      </div>
    </div>
  `;
  
  return col;
}

function 显示热门商品() {
  const hotProducts = allNewProducts.filter(p => p.想要人数 >= 10).slice(0, 5);
  const container = document.getElementById('hot-products-container');
  
  if (hotProducts.length === 0) {
    container.innerHTML = `
      <div class="carousel-item active">
        <div class="text-center py-4">
          <i class="fas fa-star fa-2x text-muted mb-2"></i>
          <p class="text-muted">暂无热门新品</p>
        </div>
      </div>
    `;
    return;
  }
  
  container.innerHTML = '';
  
  hotProducts.forEach((product, index) => {
    const item = document.createElement('div');
    item.className = `carousel-item ${index === 0 ? 'active' : ''}`;
    
    item.innerHTML = `
      <div class="row align-items-center py-3">
        <div class="col-md-8">
          <h6 class="mb-1">${product.商品标题}</h6>
          <p class="text-muted mb-1">
            <i class="fas fa-user me-1"></i>${product.卖家名称} | 
            <i class="fas fa-gamepad me-1"></i>${product.游戏名称}
          </p>
          <small class="text-muted">${计算时间差(product.上架时间)}上架</small>
        </div>
        <div class="col-md-4 text-end">
          <span class="badge bg-danger fs-6">
            <i class="fas fa-fire me-1"></i>${product.想要人数}人想要
          </span>
          <div class="mt-2">
            <button class="btn btn-sm btn-primary" onclick="查看商品详情('${product.商品ID}')">
              查看详情
            </button>
          </div>
        </div>
      </div>
    `;
    
    container.appendChild(item);
  });
}

function 加载卖家选项() {
  const sellerFilter = document.getElementById('seller-filter');
  const sellers = [...new Set(allNewProducts.map(p => ({ id: p.卖家ID, name: p.卖家名称 })))];
  
  // 清空现有选项（保留"全部卖家"）
  sellerFilter.innerHTML = '<option value="">全部卖家</option>';
  
  sellers.forEach(seller => {
    const option = document.createElement('option');
    option.value = seller.id;
    option.textContent = seller.name;
    sellerFilter.appendChild(option);
  });
}

function 筛选新商品() {
  const timeFilter = document.getElementById('time-filter').value;
  const sellerFilter = document.getElementById('seller-filter').value;
  const wantFilter = document.getElementById('want-filter').value;
  
  filteredProducts = allNewProducts.filter(product => {
    // 时间筛选
    const productTime = new Date(product.上架时间);
    const now = new Date();
    
    switch (timeFilter) {
      case 'today':
        if (productTime.toDateString() !== now.toDateString()) return false;
        break;
      case 'yesterday':
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        if (productTime.toDateString() !== yesterday.toDateString()) return false;
        break;
      case 'week':
        const weekAgo = new Date(now);
        weekAgo.setDate(weekAgo.getDate() - 7);
        if (productTime < weekAgo) return false;
        break;
      case 'month':
        const monthAgo = new Date(now);
        monthAgo.setMonth(monthAgo.getMonth() - 1);
        if (productTime < monthAgo) return false;
        break;
    }
    
    // 卖家筛选
    if (sellerFilter && product.卖家ID !== sellerFilter) {
      return false;
    }
    
    // 想要人数筛选
    if (wantFilter) {
      const wantCount = product.想要人数;
      switch (wantFilter) {
        case '0':
          if (wantCount !== 0) return false;
          break;
        case '1-5':
          if (wantCount < 1 || wantCount > 5) return false;
          break;
        case '6-20':
          if (wantCount < 6 || wantCount > 20) return false;
          break;
        case '21+':
          if (wantCount <= 20) return false;
          break;
      }
    }
    
    return true;
  });
  
  currentPage = 1;
  更新统计信息();
  显示新商品();
  显示加载状态(false);
}

function 排序新商品() {
  const sortBy = document.getElementById('sort-filter').value;
  
  filteredProducts.sort((a, b) => {
    switch (sortBy) {
      case 'time-desc':
        return new Date(b.上架时间) - new Date(a.上架时间);
      case 'want-desc':
        return b.想要人数 - a.想要人数;
      case 'want-asc':
        return a.想要人数 - b.想要人数;
      case 'title-asc':
        return a.商品标题.localeCompare(b.商品标题);
      default:
        return 0;
    }
  });
  
  显示新商品();
}

function 查看商品详情(productId) {
  const product = allNewProducts.find(p => p.商品ID === productId);
  if (!product) return;
  
  currentProductDetail = product;
  
  const content = document.getElementById('product-detail-content');
  content.innerHTML = `
    <div class="row">
      <div class="col-md-6">
        <h6>基本信息</h6>
        <table class="table table-sm">
          <tr><td>商品ID</td><td><code>${product.商品ID}</code></td></tr>
          <tr><td>商品标题</td><td>${product.商品标题}</td></tr>
          <tr><td>卖家</td><td>${product.卖家名称} (${product.卖家ID})</td></tr>
          <tr><td>游戏名称</td><td><span class="badge bg-primary">${product.游戏名称 || '未知'}</span></td></tr>
        </table>
      </div>
      <div class="col-md-6">
        <h6>统计信息</h6>
        <table class="table table-sm">
          <tr><td>想要人数</td><td><span class="badge bg-success">${product.想要人数}人</span></td></tr>
          <tr><td>上架时间</td><td>${new Date(product.上架时间).toLocaleString()}</td></tr>
          <tr><td>发现时间</td><td>${new Date(product.采集时间).toLocaleString()}</td></tr>
          <tr><td>时间差</td><td>${计算时间差(product.上架时间)}</td></tr>
        </table>
      </div>
    </div>
  `;
  
  const modal = new bootstrap.Modal(document.getElementById('product-detail-modal'));
  modal.show();
}

function 计算时间差(timeString) {
  const time = new Date(timeString);
  const now = new Date();
  const diff = now - time;
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
}

function 复制商品ID(productId) {
  复制到剪贴板(productId);
}

function 打开商品页面(productId) {
  const url = `https://www.goofish.com/item?id=${productId}`;
  window.open(url, '_blank');
}

function 打开商品链接() {
  if (currentProductDetail) {
    打开商品页面(currentProductDetail.商品ID);
  }
}

function 复制商品信息() {
  if (currentProductDetail) {
    const info = `商品标题: ${currentProductDetail.商品标题}\n商品ID: ${currentProductDetail.商品ID}\n卖家: ${currentProductDetail.卖家名称}\n想要人数: ${currentProductDetail.想要人数}人\n链接: https://www.goofish.com/item?id=${currentProductDetail.商品ID}`;
    复制到剪贴板(info);
  }
}

function 导出新商品数据() {
  if (filteredProducts.length === 0) {
    显示通知('没有数据可导出', 'warning');
    return;
  }
  
  const csvContent = [
    '商品ID,商品标题,卖家名称,卖家ID,想要人数,游戏名称,上架时间,发现时间',
    ...filteredProducts.map(product => [
      product.商品ID,
      `"${product.商品标题.replace(/"/g, '""')}"`,
      product.卖家名称,
      product.卖家ID,
      product.想要人数,
      product.游戏名称 || '',
      product.上架时间,
      product.采集时间
    ].join(','))
  ].join('\n');
  
  const filename = `新上架商品_${new Date().toISOString().slice(0, 10)}.csv`;
  下载文件('\ufeff' + csvContent, filename, 'text/csv;charset=utf-8');
  显示通知('新商品数据导出成功', 'success');
}

function 刷新新商品() {
  加载新商品数据();
}

function 更新分页() {
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const paginationContainer = document.getElementById('pagination-container');
  const pagination = document.getElementById('pagination');
  
  if (totalPages <= 1) {
    paginationContainer.style.display = 'none';
    return;
  }
  
  paginationContainer.style.display = 'block';
  pagination.innerHTML = '';
  
  // 上一页
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
  prevLi.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${currentPage - 1})">上一页</a>`;
  pagination.appendChild(prevLi);
  
  // 页码
  const startPage = Math.max(1, currentPage - 2);
  const endPage = Math.min(totalPages, currentPage + 2);
  
  for (let i = startPage; i <= endPage; i++) {
    const li = document.createElement('li');
    li.className = `page-item ${i === currentPage ? 'active' : ''}`;
    li.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${i})">${i}</a>`;
    pagination.appendChild(li);
  }
  
  // 下一页
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
  nextLi.innerHTML = `<a class="page-link" href="#" onclick="跳转页面(${currentPage + 1})">下一页</a>`;
  pagination.appendChild(nextLi);
}

function 跳转页面(page) {
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  if (page < 1 || page > totalPages) return;
  
  currentPage = page;
  显示新商品();
}

// 添加商品卡片悬停效果
document.addEventListener('DOMContentLoaded', function() {
  const style = document.createElement('style');
  style.textContent = `
    .product-card {
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
      cursor: pointer;
    }
    .product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }
    .text-truncate-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
      height: 2.8em;
    }
  `;
  document.head.appendChild(style);
});
</script>
{% endblock %}
