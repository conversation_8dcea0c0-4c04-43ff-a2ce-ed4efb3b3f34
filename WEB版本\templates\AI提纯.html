{% extends "基础模板.html" %}

{% block title %}AI提纯 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- AI提纯说明 -->
  <div class="col-12 mb-4">
    <div class="alert alert-info">
      <h5 class="alert-heading">
        <i class="fas fa-magic me-2"></i>AI提纯功能说明
      </h5>
      <p class="mb-0">
        AI提纯功能使用人工智能技术从商品标题中提取准确的游戏名称，提高数据质量。
        请确保已配置OpenAI API密钥。
      </p>
    </div>
  </div>

  <!-- 数据源选择 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-database me-2"></i>
          数据源选择
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="seller-select" class="form-label">选择卖家</label>
          <select class="form-select" id="seller-select" onchange="加载卖家商品()">
            <option value="">请选择卖家</option>
            <!-- 卖家选项将通过JavaScript动态加载 -->
          </select>
        </div>
        
        <div class="mb-3">
          <label class="form-label">商品数量统计</label>
          <div class="row text-center">
            <div class="col-6">
              <div class="border rounded p-2">
                <h4 class="text-primary mb-1" id="total-products">0</h4>
                <small class="text-muted">总商品数</small>
              </div>
            </div>
            <div class="col-6">
              <div class="border rounded p-2">
                <h4 class="text-warning mb-1" id="unpurified-products">0</h4>
                <small class="text-muted">待提纯商品</small>
              </div>
            </div>
          </div>
        </div>

        <div class="mb-3">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="only-unpurified" checked>
            <label class="form-check-label" for="only-unpurified">
              只提纯未处理的商品
            </label>
          </div>
        </div>

        <div class="d-grid">
          <button type="button" class="btn btn-primary" id="start-purify-btn" onclick="开始提纯()" disabled>
            <i class="fas fa-magic me-2"></i>
            开始AI提纯
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 提纯设置 -->
  <div class="col-md-6 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-cog me-2"></i>
          提纯设置
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="batch-size" class="form-label">批次大小</label>
          <input type="number" class="form-control" id="batch-size" value="20" min="5" max="50">
          <div class="form-text">每批处理的商品数量，建议10-30个</div>
        </div>

        <div class="mb-3">
          <label for="api-model" class="form-label">AI模型</label>
          <select class="form-select" id="api-model">
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo (推荐)</option>
            <option value="gpt-4">GPT-4 (更准确但较慢)</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="temperature" class="form-label">创造性 (Temperature)</label>
          <input type="range" class="form-range" id="temperature" min="0" max="1" step="0.1" value="0.2">
          <div class="d-flex justify-content-between">
            <small class="text-muted">保守 (0)</small>
            <small class="text-muted">创造 (1)</small>
          </div>
        </div>

        <!-- API状态检查 -->
        <div class="mb-3">
          <button type="button" class="btn btn-outline-info btn-sm" onclick="检查API状态()">
            <i class="fas fa-check-circle me-1"></i>
            检查API状态
          </button>
          <div id="api-status" class="mt-2"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 提纯进度 -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-tasks me-2"></i>
          提纯进度
        </h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <span>整体进度</span>
            <span id="progress-text">0/0 (0%)</span>
          </div>
          <div class="progress">
            <div class="progress-bar" id="progress-bar" role="progressbar" style="width: 0%"></div>
          </div>
        </div>

        <div class="row text-center">
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h5 class="text-success mb-1" id="success-count">0</h5>
              <small class="text-muted">成功提纯</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h5 class="text-danger mb-1" id="failed-count">0</h5>
              <small class="text-muted">提纯失败</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h5 class="text-info mb-1" id="current-batch">0</h5>
              <small class="text-muted">当前批次</small>
            </div>
          </div>
          <div class="col-md-3">
            <div class="border rounded p-2">
              <h5 class="text-warning mb-1" id="estimated-time">--</h5>
              <small class="text-muted">预计剩余</small>
            </div>
          </div>
        </div>

        <div class="mt-3">
          <button type="button" class="btn btn-danger" id="stop-purify-btn" onclick="停止提纯()" disabled>
            <i class="fas fa-stop me-1"></i>
            停止提纯
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 提纯结果 -->
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
          <i class="fas fa-list me-2"></i>
          提纯结果
        </h5>
        <div>
          <button type="button" class="btn btn-sm btn-outline-primary" onclick="导出提纯结果()">
            <i class="fas fa-download me-1"></i>导出结果
          </button>
          <button type="button" class="btn btn-sm btn-outline-success" onclick="应用提纯结果()">
            <i class="fas fa-save me-1"></i>应用到数据
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- 结果筛选 -->
        <div class="row mb-3">
          <div class="col-md-4">
            <select class="form-select form-select-sm" id="result-filter" onchange="筛选提纯结果()">
              <option value="">全部结果</option>
              <option value="success">提纯成功</option>
              <option value="failed">提纯失败</option>
              <option value="unknown">未知游戏</option>
            </select>
          </div>
          <div class="col-md-4">
            <input type="text" class="form-control form-control-sm" id="result-search" 
                   placeholder="搜索商品标题或游戏名称" onkeyup="搜索提纯结果(this.value)">
          </div>
          <div class="col-md-4">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="清空提纯结果()">
              <i class="fas fa-trash me-1"></i>清空结果
            </button>
          </div>
        </div>

        <!-- 结果表格 -->
        <div class="table-responsive">
          <table class="table table-hover table-sm">
            <thead class="table-light">
              <tr>
                <th style="width: 50%">原始标题</th>
                <th style="width: 20%">提纯游戏名称</th>
                <th style="width: 15%">状态</th>
                <th style="width: 15%">操作</th>
              </tr>
            </thead>
            <tbody id="purify-results-body">
              <!-- 结果将通过JavaScript动态加载 -->
            </tbody>
          </table>
        </div>

        <!-- 空结果提示 -->
        <div id="empty-results" class="text-center py-4" style="display: none;">
          <i class="fas fa-flask fa-2x text-muted mb-2"></i>
          <p class="text-muted">暂无提纯结果</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 手动编辑模态框 -->
<div class="modal fade" id="edit-result-modal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">手动编辑游戏名称</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">原始标题</label>
          <p class="form-control-plaintext" id="edit-original-title"></p>
        </div>
        <div class="mb-3">
          <label for="edit-game-name" class="form-label">游戏名称</label>
          <input type="text" class="form-control" id="edit-game-name" placeholder="输入正确的游戏名称">
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="保存编辑结果()">保存</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let purifyResults = [];
let filteredResults = [];
let currentEditIndex = -1;
let isPurifying = false;
let purifyStats = {
  total: 0,
  processed: 0,
  success: 0,
  failed: 0,
  currentBatch: 0
};

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  加载卖家列表();
  初始化事件监听();
});

function 初始化事件监听() {
  // 监听状态更新
  document.addEventListener('statusUpdate', function(event) {
    const data = event.detail;
    if (data.message.includes('提纯')) {
      处理提纯状态更新(data);
    }
  });
}

function 加载卖家列表() {
  fetch('/api/卖家列表')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        显示卖家列表(data.data);
      } else {
        显示通知('加载卖家列表失败: ' + data.error, 'danger');
      }
    })
    .catch(error => {
      console.error('加载卖家列表失败:', error);
      显示通知('网络错误，无法加载卖家列表', 'danger');
    });
}

function 显示卖家列表(sellers) {
  const select = document.getElementById('seller-select');
  select.innerHTML = '<option value="">请选择卖家</option>';
  
  sellers.forEach(seller => {
    const option = document.createElement('option');
    option.value = seller.卖家ID;
    option.textContent = `${seller.卖家名称} (${seller.卖家ID})`;
    select.appendChild(option);
  });
}

function 加载卖家商品() {
  const sellerId = document.getElementById('seller-select').value;
  if (!sellerId) {
    document.getElementById('total-products').textContent = '0';
    document.getElementById('unpurified-products').textContent = '0';
    document.getElementById('start-purify-btn').disabled = true;
    return;
  }

  // 调用API获取真实的卖家商品数据
  加载卖家商品数据(sellerId);
}

function 加载卖家商品数据(sellerId) {
  // 调用API获取真实的卖家商品数据
  fetch(`/api/获取商品数据?卖家ID=${sellerId}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const totalProducts = data.data.length;
        const unpurifiedProducts = data.data.filter(item => !item.游戏名称 || item.游戏名称 === '').length;

        document.getElementById('total-products').textContent = totalProducts;
        document.getElementById('unpurified-products').textContent = unpurifiedProducts;
        document.getElementById('start-purify-btn').disabled = unpurifiedProducts === 0;
      } else {
        document.getElementById('total-products').textContent = '0';
        document.getElementById('unpurified-products').textContent = '0';
        document.getElementById('start-purify-btn').disabled = true;
        显示通知('获取商品数据失败: ' + data.error, 'danger');
      }
    })
    .catch(error => {
      console.error('获取商品数据失败:', error);
      document.getElementById('total-products').textContent = '0';
      document.getElementById('unpurified-products').textContent = '0';
      document.getElementById('start-purify-btn').disabled = true;
      显示通知('网络错误，无法获取商品数据', 'danger');
    });
}

function 检查API状态() {
  const statusDiv = document.getElementById('api-status');
  statusDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>检查中...';
  
  // 这里应该调用API检查状态
  setTimeout(() => {
    // 模拟API状态检查结果
    const isValid = Math.random() > 0.3;
    if (isValid) {
      statusDiv.innerHTML = '<div class="text-success"><i class="fas fa-check-circle me-1"></i>API连接正常</div>';
    } else {
      statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-times-circle me-1"></i>API连接失败，请检查密钥配置</div>';
    }
  }, 1000);
}

function 开始提纯() {
  const sellerId = document.getElementById('seller-select').value;
  if (!sellerId) {
    显示通知('请先选择卖家', 'warning');
    return;
  }
  
  const batchSize = parseInt(document.getElementById('batch-size').value);
  const model = document.getElementById('api-model').value;
  const temperature = parseFloat(document.getElementById('temperature').value);
  const onlyUnpurified = document.getElementById('only-unpurified').checked;
  
  // 设置提纯状态
  设置提纯状态(true);
  
  // 初始化统计
  purifyStats = {
    total: parseInt(document.getElementById('unpurified-products').textContent),
    processed: 0,
    success: 0,
    failed: 0,
    currentBatch: 1
  };
  
  更新提纯进度();
  
  // 模拟提纯过程
  模拟提纯过程();
}

function 模拟提纯过程() {
  const batchSize = parseInt(document.getElementById('batch-size').value);
  const totalBatches = Math.ceil(purifyStats.total / batchSize);
  
  let currentBatch = 0;
  
  const processBatch = () => {
    if (!isPurifying || currentBatch >= totalBatches) {
      设置提纯状态(false);
      显示通知('AI提纯完成', 'success');
      return;
    }
    
    currentBatch++;
    purifyStats.currentBatch = currentBatch;
    
    // 模拟批次处理
    const batchStart = (currentBatch - 1) * batchSize;
    const batchEnd = Math.min(batchStart + batchSize, purifyStats.total);
    
    for (let i = batchStart; i < batchEnd; i++) {
      // 模拟提纯结果
      const isSuccess = Math.random() > 0.2;
      const result = {
        原标题: `模拟商品标题 ${i + 1} - 王者荣耀皮肤代练上分`,
        游戏名称: isSuccess ? ['王者荣耀', '原神', '和平精英', '英雄联盟'][Math.floor(Math.random() * 4)] : '未知',
        状态: isSuccess ? 'success' : 'failed'
      };
      
      purifyResults.push(result);
      
      if (isSuccess) {
        purifyStats.success++;
      } else {
        purifyStats.failed++;
      }
      
      purifyStats.processed++;
    }
    
    更新提纯进度();
    显示提纯结果();
    
    // 继续下一批次
    setTimeout(processBatch, 2000);
  };
  
  processBatch();
}

function 停止提纯() {
  isPurifying = false;
  设置提纯状态(false);
  显示通知('AI提纯已停止', 'info');
}

function 设置提纯状态(purifying) {
  isPurifying = purifying;
  
  document.getElementById('start-purify-btn').disabled = purifying;
  document.getElementById('stop-purify-btn').disabled = !purifying;
  document.getElementById('seller-select').disabled = purifying;
}

function 更新提纯进度() {
  const { total, processed, success, failed, currentBatch } = purifyStats;
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
  
  document.getElementById('progress-text').textContent = `${processed}/${total} (${percentage}%)`;
  document.getElementById('progress-bar').style.width = `${percentage}%`;
  
  document.getElementById('success-count').textContent = success;
  document.getElementById('failed-count').textContent = failed;
  document.getElementById('current-batch').textContent = currentBatch;
  
  // 估算剩余时间
  if (processed > 0 && processed < total) {
    const avgTimePerItem = 2; // 假设每个商品2秒
    const remainingItems = total - processed;
    const remainingMinutes = Math.ceil((remainingItems * avgTimePerItem) / 60);
    document.getElementById('estimated-time').textContent = `${remainingMinutes}分钟`;
  } else {
    document.getElementById('estimated-time').textContent = '--';
  }
}

function 显示提纯结果() {
  filteredResults = [...purifyResults];
  渲染提纯结果();
}

function 渲染提纯结果() {
  const tbody = document.getElementById('purify-results-body');
  const emptyDiv = document.getElementById('empty-results');
  
  if (filteredResults.length === 0) {
    tbody.innerHTML = '';
    emptyDiv.style.display = 'block';
    return;
  }
  
  emptyDiv.style.display = 'none';
  tbody.innerHTML = '';
  
  filteredResults.forEach((result, index) => {
    const row = document.createElement('tr');
    
    const statusClass = result.状态 === 'success' ? 'success' : 'danger';
    const statusText = result.状态 === 'success' ? '成功' : '失败';
    const statusIcon = result.状态 === 'success' ? 'check-circle' : 'times-circle';
    
    row.innerHTML = `
      <td>
        <div class="text-truncate" title="${result.原标题}">
          ${result.原标题}
        </div>
      </td>
      <td>
        <span class="badge ${result.游戏名称 === '未知' ? 'bg-secondary' : 'bg-primary'}">
          ${result.游戏名称}
        </span>
      </td>
      <td>
        <span class="badge bg-${statusClass}">
          <i class="fas fa-${statusIcon} me-1"></i>${statusText}
        </span>
      </td>
      <td>
        <button class="btn btn-sm btn-outline-primary" onclick="编辑结果(${index})" title="编辑">
          <i class="fas fa-edit"></i>
        </button>
      </td>
    `;
    
    tbody.appendChild(row);
  });
}

function 筛选提纯结果() {
  const filter = document.getElementById('result-filter').value;
  
  if (!filter) {
    filteredResults = [...purifyResults];
  } else {
    filteredResults = purifyResults.filter(result => {
      switch (filter) {
        case 'success':
          return result.状态 === 'success';
        case 'failed':
          return result.状态 === 'failed';
        case 'unknown':
          return result.游戏名称 === '未知';
        default:
          return true;
      }
    });
  }
  
  渲染提纯结果();
}

function 搜索提纯结果(keyword) {
  if (!keyword.trim()) {
    筛选提纯结果();
    return;
  }
  
  const lowerKeyword = keyword.toLowerCase();
  filteredResults = purifyResults.filter(result => {
    return result.原标题.toLowerCase().includes(lowerKeyword) ||
           result.游戏名称.toLowerCase().includes(lowerKeyword);
  });
  
  渲染提纯结果();
}

function 编辑结果(index) {
  const result = filteredResults[index];
  currentEditIndex = purifyResults.indexOf(result);
  
  document.getElementById('edit-original-title').textContent = result.原标题;
  document.getElementById('edit-game-name').value = result.游戏名称;
  
  const modal = new bootstrap.Modal(document.getElementById('edit-result-modal'));
  modal.show();
}

function 保存编辑结果() {
  const newGameName = document.getElementById('edit-game-name').value.trim();
  if (!newGameName) {
    显示通知('请输入游戏名称', 'warning');
    return;
  }
  
  if (currentEditIndex >= 0) {
    purifyResults[currentEditIndex].游戏名称 = newGameName;
    purifyResults[currentEditIndex].状态 = 'success';
    
    显示提纯结果();
    显示通知('游戏名称已更新', 'success');
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('edit-result-modal'));
    modal.hide();
  }
}

function 清空提纯结果() {
  if (purifyResults.length === 0) return;
  
  if (confirm('确定要清空所有提纯结果吗？')) {
    purifyResults = [];
    filteredResults = [];
    渲染提纯结果();
    显示通知('提纯结果已清空', 'info');
  }
}

function 导出提纯结果() {
  if (purifyResults.length === 0) {
    显示通知('没有提纯结果可导出', 'warning');
    return;
  }
  
  const csvContent = [
    '原标题,游戏名称,状态',
    ...purifyResults.map(result => 
      `"${result.原标题.replace(/"/g, '""')}","${result.游戏名称}","${result.状态}"`
    )
  ].join('\n');
  
  const filename = `AI提纯结果_${new Date().toISOString().slice(0, 10)}.csv`;
  下载文件('\ufeff' + csvContent, filename, 'text/csv;charset=utf-8');
  显示通知('提纯结果导出成功', 'success');
}

function 应用提纯结果() {
  const successResults = purifyResults.filter(result => result.状态 === 'success');
  
  if (successResults.length === 0) {
    显示通知('没有成功的提纯结果可应用', 'warning');
    return;
  }
  
  if (confirm(`确定要将 ${successResults.length} 个提纯结果应用到数据中吗？`)) {
    // 这里应该调用API将提纯结果应用到数据
    显示通知(`已应用 ${successResults.length} 个提纯结果到数据中`, 'success');
  }
}

function 处理提纯状态更新(data) {
  // 处理来自后端的提纯状态更新
  if (data.message.includes('开始提纯')) {
    设置提纯状态(true);
  } else if (data.message.includes('提纯完成') || data.message.includes('提纯停止')) {
    设置提纯状态(false);
  }
}
</script>
{% endblock %}
