/**
 * 闲鱼商品采集工具 WEB版本 - 主脚本文件
 */

// 全局变量
let socket = null;
let isConnected = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    初始化应用();
});

/**
 * 初始化应用
 */
function 初始化应用() {
    初始化Socket连接();
    初始化导航栏();
    初始化工具提示();
    更新状态指示器();
}

/**
 * 初始化Socket.IO连接
 */
function 初始化Socket连接() {
    try {
        socket = io();
        
        socket.on('connect', function() {
            isConnected = true;
            console.log('已连接到服务器');
            更新连接状态(true);
        });
        
        socket.on('disconnect', function() {
            isConnected = false;
            console.log('与服务器连接断开');
            更新连接状态(false);
        });
        
        socket.on('status_update', function(data) {
            处理状态更新(data);
        });
        
        socket.on('connect_error', function(error) {
            console.error('连接错误:', error);
            更新连接状态(false);
        });
        
    } catch (error) {
        console.error('初始化Socket连接失败:', error);
    }
}

/**
 * 处理状态更新
 */
function 处理状态更新(data) {
    const { message, type, timestamp } = data;
    
    // 更新状态指示器
    if (message.includes('采集') || message.includes('运行')) {
        if (message.includes('开始') || message.includes('运行中')) {
            更新状态指示器('运行中', 'warning');
        } else if (message.includes('完成') || message.includes('结束') || message.includes('空闲')) {
            更新状态指示器('空闲', 'success');
        }
    }
    
    // 显示消息通知
    if (type === 'error') {
        显示通知(message, 'danger');
    } else if (type === 'success') {
        显示通知(message, 'success');
    } else if (type === 'warning') {
        显示通知(message, 'warning');
    }
    
    // 触发自定义事件，让其他页面可以监听
    document.dispatchEvent(new CustomEvent('statusUpdate', {
        detail: data
    }));
}

/**
 * 更新连接状态
 */
function 更新连接状态(connected) {
    const statusIndicator = document.getElementById('status-indicator');
    if (statusIndicator) {
        if (connected) {
            statusIndicator.innerHTML = '<i class="fas fa-circle me-1"></i>已连接';
            statusIndicator.className = 'badge bg-success';
        } else {
            statusIndicator.innerHTML = '<i class="fas fa-circle me-1"></i>连接断开';
            statusIndicator.className = 'badge bg-danger';
        }
    }
}

/**
 * 更新状态指示器
 */
function 更新状态指示器(status = '空闲', type = 'success') {
    const statusIndicator = document.getElementById('status-indicator');
    if (statusIndicator) {
        const icons = {
            '空闲': 'fas fa-circle',
            '运行中': 'fas fa-spinner fa-spin',
            '错误': 'fas fa-exclamation-circle'
        };
        
        const colors = {
            'success': 'bg-success',
            'warning': 'bg-warning',
            'danger': 'bg-danger',
            'info': 'bg-info'
        };
        
        const icon = icons[status] || 'fas fa-circle';
        const color = colors[type] || 'bg-success';
        
        statusIndicator.innerHTML = `<i class="${icon} me-1"></i>${status}`;
        statusIndicator.className = `badge ${color}`;
    }
}

/**
 * 初始化导航栏
 */
function 初始化导航栏() {
    // 高亮当前页面的导航项
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

/**
 * 初始化工具提示
 */
function 初始化工具提示() {
    // 初始化Bootstrap工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 显示通知消息
 */
function 显示通知(message, type = 'info', duration = 5000) {
    const container = document.getElementById('message-container');
    if (!container) return;
    
    const alertTypes = {
        'success': 'alert-success',
        'danger': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const icons = {
        'success': 'fas fa-check-circle',
        'danger': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    
    const alertClass = alertTypes[type] || 'alert-info';
    const icon = icons[type] || 'fas fa-info-circle';
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert ${alertClass} alert-dismissible fade show`;
    alertElement.innerHTML = `
        <i class="${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    container.appendChild(alertElement);
    
    // 自动移除通知
    if (duration > 0) {
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, duration);
    }
}

/**
 * 发送API请求的通用函数
 */
async function 发送API请求(url, options = {}) {
    try {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        const response = await fetch(url, mergedOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 格式化时间戳
 */
function 格式化时间(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 格式化文件大小
 */
function 格式化文件大小(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 防抖函数
 */
function 防抖(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function 节流(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 复制文本到剪贴板
 */
async function 复制到剪贴板(text) {
    try {
        await navigator.clipboard.writeText(text);
        显示通知('已复制到剪贴板', 'success', 2000);
        return true;
    } catch (error) {
        console.error('复制失败:', error);
        显示通知('复制失败', 'danger', 2000);
        return false;
    }
}

/**
 * 下载文件
 */
function 下载文件(data, filename, type = 'text/plain') {
    const blob = new Blob([data], { type });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

/**
 * 验证表单数据
 */
function 验证表单(formElement) {
    const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * 清除表单验证状态
 */
function 清除表单验证(formElement) {
    const inputs = formElement.querySelectorAll('.is-invalid, .is-valid');
    inputs.forEach(input => {
        input.classList.remove('is-invalid', 'is-valid');
    });
}

/**
 * 显示加载状态
 */
function 显示加载状态(element, loading = true) {
    if (loading) {
        element.disabled = true;
        const originalText = element.textContent;
        element.dataset.originalText = originalText;
        element.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';
    } else {
        element.disabled = false;
        element.textContent = element.dataset.originalText || '确定';
    }
}

/**
 * 确认对话框
 */
function 确认对话框(message, title = '确认操作') {
    return new Promise((resolve) => {
        if (confirm(`${title}\n\n${message}`)) {
            resolve(true);
        } else {
            resolve(false);
        }
    });
}

/**
 * 获取URL参数
 */
function 获取URL参数(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 设置URL参数
 */
function 设置URL参数(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
}

/**
 * 页面可见性变化处理
 */
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时的处理
        console.log('页面已隐藏');
    } else {
        // 页面显示时的处理
        console.log('页面已显示');
        // 重新连接Socket（如果需要）
        if (!isConnected && socket) {
            socket.connect();
        }
    }
});

/**
 * 窗口大小变化处理
 */
window.addEventListener('resize', 防抖(function() {
    // 响应式处理
    console.log('窗口大小已变化');
}, 250));

/**
 * 全局错误处理
 */
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    显示通知('发生了一个错误，请刷新页面重试', 'danger');
});

/**
 * 未处理的Promise拒绝
 */
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise拒绝:', event.reason);
    显示通知('操作失败，请重试', 'danger');
});

// 导出常用函数到全局作用域
window.显示通知 = 显示通知;
window.发送API请求 = 发送API请求;
window.格式化时间 = 格式化时间;
window.复制到剪贴板 = 复制到剪贴板;
window.下载文件 = 下载文件;
window.确认对话框 = 确认对话框;
