@echo off
chcp 65001 >nul
title 闲鱼商品采集工具 - WEB版本

echo ========================================
echo 闲鱼商品采集工具 - WEB版本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到Python版本:
python --version

:: 安装依赖
echo.
echo 检查并安装依赖包...
pip install -r requirements.txt

:: 启动应用
echo.
echo 启动WEB应用...
echo 请稍等，浏览器将自动打开...
echo.
echo 如果浏览器没有自动打开，请手动访问: http://127.0.0.1:5000
echo 按 Ctrl+C 可以停止应用
echo.

python 启动.py

echo.
echo 应用已停止
pause
