{% extends "基础模板.html" %}

{% block title %}主页 - 闲鱼商品采集工具{% endblock %}

{% block content %}
<div class="row">
  <!-- 欢迎区域 -->
  <div class="col-12 mb-4">
    <div class="card bg-primary text-white">
      <div class="card-body text-center">
        <h1 class="card-title">
          <i class="fas fa-shopping-cart me-3"></i>
          闲鱼商品采集工具 WEB版本
        </h1>
        <p class="card-text lead">
          专业的闲鱼卖家商品数据采集与分析工具
        </p>
      </div>
    </div>
  </div>

  <!-- 功能卡片区域 -->
  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-download"></i>
        </div>
        <h5 class="card-title">数据采集</h5>
        <p class="card-text">
          支持单个卖家和批量卖家的商品数据采集，自动检测新上架商品
        </p>
        <a href="{{ url_for('数据采集页面') }}" class="btn btn-primary">
          开始采集 <i class="fas fa-arrow-right ms-1"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-table"></i>
        </div>
        <h5 class="card-title">数据查看</h5>
        <p class="card-text">
          查看和管理已采集的商品数据，支持搜索、筛选和导出功能
        </p>
        <a href="{{ url_for('数据查看页面') }}" class="btn btn-success">
          查看数据 <i class="fas fa-arrow-right ms-1"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-magic"></i>
        </div>
        <h5 class="card-title">AI提纯</h5>
        <p class="card-text">
          使用AI技术从商品标题中提取准确的游戏名称，提高数据质量
        </p>
        <a href="{{ url_for('AI提纯页面') }}" class="btn btn-warning">
          AI提纯 <i class="fas fa-arrow-right ms-1"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-info text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-star"></i>
        </div>
        <h5 class="card-title">新上架商品</h5>
        <p class="card-text">
          实时监控新上架的商品，按想要人数排序，发现热门商品
        </p>
        <a href="{{ url_for('新上架商品页面') }}" class="btn btn-info">
          查看新品 <i class="fas fa-arrow-right ms-1"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-secondary text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-cog"></i>
        </div>
        <h5 class="card-title">系统设置</h5>
        <p class="card-text">
          配置系统参数、管理卖家信息、设置Cookie和API密钥
        </p>
        <a href="{{ url_for('系统设置页面') }}" class="btn btn-secondary">
          系统设置 <i class="fas fa-arrow-right ms-1"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 shadow-sm">
      <div class="card-body text-center">
        <div class="feature-icon bg-dark text-white rounded-circle mx-auto mb-3">
          <i class="fas fa-chart-bar"></i>
        </div>
        <h5 class="card-title">数据统计</h5>
        <p class="card-text">
          查看采集统计信息、数据趋势分析和系统运行状态
        </p>
        <button class="btn btn-dark" onclick="显示统计信息()">
          查看统计 <i class="fas fa-arrow-right ms-1"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 快速状态面板 -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-tachometer-alt me-2"></i>
          系统状态
        </h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="text-primary" id="seller-count">-</h3>
              <p class="text-muted">已配置卖家</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="text-success" id="collection-status">空闲</h3>
              <p class="text-muted">采集状态</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="text-info" id="data-files">-</h3>
              <p class="text-muted">数据文件</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <h3 class="text-warning" id="last-update">-</h3>
              <p class="text-muted">最后更新</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 最近活动 -->
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">
          <i class="fas fa-history me-2"></i>
          最近活动
        </h5>
      </div>
      <div class="card-body">
        <div id="recent-activities">
          <p class="text-muted text-center">暂无活动记录</p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时获取系统状态
document.addEventListener('DOMContentLoaded', function() {
  加载系统状态();
});

function 加载系统状态() {
  // 获取卖家数量
  fetch('/api/卖家列表')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        document.getElementById('seller-count').textContent = data.data.length;
      }
    })
    .catch(error => console.error('获取卖家列表失败:', error));
  
  // 获取采集状态
  fetch('/api/采集状态')
    .then(response => response.json())
    .then(data => {
      const statusElement = document.getElementById('collection-status');
      statusElement.textContent = data.status === '运行中' ? '采集中' : '空闲';
      statusElement.className = data.status === '运行中' ? 'text-warning' : 'text-success';
    })
    .catch(error => console.error('获取采集状态失败:', error));
  
  // 设置当前时间
  document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
}

function 显示统计信息() {
  alert('统计功能正在开发中...');
}

// 每30秒更新一次状态
setInterval(加载系统状态, 30000);
</script>
{% endblock %}
