/* 闲鱼商品采集工具 WEB版本 - 主样式文件 */

/* 全局样式 */
body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
  font-weight: bold;
  font-size: 1.25rem;
}

.navbar-nav .nav-link {
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 功能图标样式 */
.feature-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

/* 卡片样式 */
.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

/* 统计项样式 */
.stat-item h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.stat-item p {
  font-size: 0.875rem;
  margin-bottom: 0;
}

/* 按钮样式 */
.btn {
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

/* 表格样式 */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}

/* 表单样式 */
.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* 日志容器样式 */
#log-container {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.4;
  background-color: #1e1e1e !important;
  color: #d4d4d4 !important;
  border: 1px solid #333;
}

.log-entry {
  margin-bottom: 0.25rem;
  word-wrap: break-word;
}

.log-entry:last-child {
  margin-bottom: 0;
}

/* 状态指示器样式 */
#status-indicator {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* 警告框样式 */
.alert {
  border: none;
  border-radius: 0.5rem;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-success {
  background-color: #d1e7dd;
  color: #0f5132;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* 加载动画 */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feature-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .stat-item h3 {
    font-size: 1.5rem;
  }
  
  .btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
  
  #log-container {
    height: 300px !important;
  }
}

@media (max-width: 576px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn {
    font-size: 0.875rem;
  }
}

/* 自定义滚动条 */
#log-container::-webkit-scrollbar {
  width: 8px;
}

#log-container::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 4px;
}

#log-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

#log-container::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* 工具提示样式 */
.tooltip {
  font-size: 0.875rem;
}

/* 进度条样式 */
.progress {
  height: 0.5rem;
  border-radius: 0.25rem;
}

.progress-bar {
  transition: width 0.3s ease;
}

/* 徽章样式 */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 分页样式 */
.pagination {
  margin-bottom: 0;
}

.page-link {
  color: #0d6efd;
  border-color: #dee2e6;
}

.page-link:hover {
  color: #0a58ca;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* 模态框样式 */
.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1.5rem;
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

/* 输入组样式 */
.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #495057;
}

/* 列表组样式 */
.list-group-item {
  border-color: #dee2e6;
  transition: background-color 0.15s ease-in-out;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.list-group-item.active {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

/* 面包屑样式 */
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #6c757d;
}

/* 自定义工具类 */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cursor-pointer {
  cursor: pointer;
}

.border-dashed {
  border-style: dashed !important;
}

.bg-light-blue {
  background-color: #e3f2fd;
}

.text-light-blue {
  color: #1976d2;
}

/* 打印样式 */
@media print {
  .navbar,
  .btn,
  .card-header,
  #log-container {
    display: none !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #dee2e6 !important;
  }
  
  body {
    background-color: white !important;
  }
}
