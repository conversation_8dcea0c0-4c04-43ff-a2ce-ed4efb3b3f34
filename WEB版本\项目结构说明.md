# 闲鱼商品采集工具 WEB版本 - 项目结构说明

## 项目目录结构

```
WEB版本/
├── 主程序.py                    # Flask Web应用主程序
├── 启动.py                      # 应用启动脚本
├── 启动WEB版本.bat              # Windows批处理启动脚本
├── 测试.py                      # 模块功能测试脚本
├── requirements.txt             # Python依赖包列表
├── README.md                    # 项目说明文档
├── 项目结构说明.md              # 本文件
│
├── modules/                     # 核心功能模块
│   ├── __init__.py             # 模块包初始化文件
│   ├── 配置管理.py              # 配置文件管理模块
│   ├── Cookie管理.py            # Cookie获取和管理模块
│   ├── 数据采集.py              # 数据采集核心模块
│   ├── 数据处理.py              # 数据处理和存储模块
│   └── ai_提纯.py               # AI提纯功能模块
│
├── templates/                   # HTML模板文件
│   ├── 基础模板.html            # 基础页面模板
│   ├── 主页.html                # 首页模板
│   ├── 数据采集.html            # 数据采集页面模板
│   ├── 数据查看.html            # 数据查看页面模板
│   ├── AI提纯.html              # AI提纯页面模板
│   ├── 系统设置.html            # 系统设置页面模板
│   └── 新上架商品.html          # 新上架商品页面模板
│
├── static/                      # 静态资源文件
│   ├── css/
│   │   └── 主样式.css           # 主要CSS样式文件
│   └── js/
│       └── 主脚本.js            # 主要JavaScript文件
│
├── 配置/                        # 配置文件目录
│   ├── 闲鱼配置.ini.example     # 配置文件示例
│   └── api_keys.txt.example     # API密钥文件示例
│
├── 数据/                        # 数据存储目录
│   ├── 卖家数据/                # 采集的卖家商品数据
│   ├── 新上架商品/              # 新上架商品记录
│   └── 提纯结果/                # AI提纯结果
│
└── 日志/                        # 日志文件目录
    └── (运行时生成的日志文件)
```

## 核心模块说明

### 1. 主程序.py
- Flask Web应用的主入口
- 定义所有的路由和API接口
- 集成各个功能模块
- 提供WebSocket实时通信

### 2. modules/ 目录
包含所有核心功能模块，从GUI版本移植而来：

#### 配置管理.py
- 管理系统配置文件
- 处理卖家信息的增删改查
- 管理默认设置和系统参数

#### Cookie管理.py
- 自动获取和管理Cookie
- 验证Cookie有效性
- 支持手动设置和自动刷新

#### 数据采集.py
- 核心的数据采集功能
- 支持单个和批量卖家采集
- 自动重试和错误处理

#### 数据处理.py
- 数据存储和管理
- 新上架商品检测
- 数据导出功能

#### ai_提纯.py
- OpenAI API集成
- 商品标题智能提纯
- 游戏名称提取

## 使用说明

### 快速启动
1. 安装依赖: `pip install -r requirements.txt`
2. 启动应用: `python 启动.py` 或双击 `启动WEB版本.bat`
3. 浏览器访问: `http://127.0.0.1:5000`

### 首次配置
1. 进入系统设置页面
2. 配置Cookie信息
3. 配置OpenAI API密钥（可选）

### 基本操作
1. 添加卖家信息
2. 开始数据采集
3. 查看和管理数据
4. 使用AI提纯功能
5. 监控新上架商品

## 技术栈

- **后端**: Python 3.8+, Flask, Flask-SocketIO
- **前端**: HTML5, CSS3, JavaScript, Bootstrap 5
- **数据存储**: JSON文件
- **浏览器自动化**: Playwright
- **AI服务**: OpenAI API

## 注意事项

1. 请遵守闲鱼平台的使用条款
2. 合理控制采集频率
3. 妥善保管API密钥和Cookie
4. 定期更新依赖包
